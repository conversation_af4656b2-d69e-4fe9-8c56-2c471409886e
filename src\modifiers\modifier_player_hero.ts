import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_player_hero extends BaseModifier {


    // OnCreated(params: object): void {
    //     if (IsClient()) {
            
    //     }
    // }
    // OnRemoved(): void {
    //     if (IsClient()) {
    //         let hero = this.GetParent() as CDOTA_BaseNPC_Hero
            
    //     }
    // }

    IsDebuff(): boolean {
        return false
    }
    
    IsPurgable(): boolean {
        return false
    }

    IsPurgeException(): boolean {
        return false;
    }

    GetAttributes(): ModifierAttribute {
        return ModifierAttribute.PERMANENT + ModifierAttribute.IGNORE_INVULNERABLE //+ ModifierAttribute.MULTIPLE
    }

    IsHidden():boolean {
        return false;
    }



    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            // [ModifierState.INVISIBLE]: true,
            // [ModifierState.SILENCED]: true,
            // [ModifierState.CANNOT_BE_MOTION_CONTROLLED]: true,
            [ModifierState.NOT_ON_MINIMAP]: true,
            // [ModifierState.STUNNED]: true,
            // [ModifierState.INVULNERABLE]: true,
            [ModifierState.NO_UNIT_COLLISION]: true,
            [ModifierState.OUT_OF_GAME]: true,
            [ModifierState.DISARMED]: true,
            [ModifierState.NO_HEALTH_BAR]: true,
            // [ModifierState.MAGIC_IMMUNE]: true,
            // [ModifierState.DEBUFF_IMMUNE]: true,
            [ModifierState.UNSELECTABLE]: true,
            [ModifierState.CANNOT_BE_MOTION_CONTROLLED]: true,
          }
        
        return state
    }

    GetModifierIncomingDamageConstant(event: ModifierAttackEvent): number {
        if (IsClient()) {
            return 0
        } else {
            return -event.damage
        }
    }

    RemoveOnDeath(): boolean {
        return true
    }


    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.INCOMING_DAMAGE_CONSTANT,
            ];
    }


}
