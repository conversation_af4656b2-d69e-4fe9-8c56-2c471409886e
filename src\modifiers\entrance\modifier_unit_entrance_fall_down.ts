
import { BaseModifier, registerModifier } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";
import { modifier_unit_card_duration } from "../modifier_unit_card_duration";
import { modifier_unit_using_delay } from "../modifier_unit_using_delay";


@registerModifier()
export class modifier_unit_entrance_fall_down extends BaseModifier {


    IsHidden():boolean {
        return true;
    }

    GetOverrideAnimation() {
        return GameActivity.DOTA_SPAWN;
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            ModifierFunction.OVERRIDE_ANIMATION,
            ModifierFunction.TRANSLATE_ACTIVITY_MODIFIERS,
            ModifierFunction.VISUAL_Z_DELTA,
        ];
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        return {
            [ModifierState.STUNNED]: true,
        }
    }

    delay:number;
    unitDuration:number;
    startTime:number;
    OnCreated(keys: any): void {
        this.fallDownTime = this.GetDuration() - 0.05
        this.allTimeIndex = MathUtils.div(this.fallDownTime,0.03)
        if (IsServer()) {
            let unit = this.GetParent()
            this.startTime = GameRules.GetGameTime()
            this.delay = Math.max(keys.delayDuration-this.fallDownTime,0)
            this.unitDuration = keys.unitDuration
            unit.SetAbsOrigin(GetGroundPosition(Vector(keys.posX,keys.posY,0),unit))
        }
    } 

    h:number =  2000
    fallDownTime:number;
    timeIndex:number = 0
    allTimeIndex:number;
    GetVisualZDelta(): number {
        print(GameRules.GetGameTime())
        let l = MathUtils.getVLengthByRate(this.h, -1, this.timeIndex, this.allTimeIndex)
        this.timeIndex += 1
        return this.h - l
    }

    OnDestroy(){
        if (IsServer()) {
            let unit = this.GetParent()
            if (this.delay > 0) {
                unit.AddNewModifier(unit, undefined, modifier_unit_using_delay.name, {duration:this.delay, unitDuration:this.unitDuration})
            } else {
                if (this.unitDuration > 0) {
                    unit.AddNewModifier(unit, undefined, modifier_unit_card_duration.name, {duration:this.unitDuration+1,fastWarDuration:this.unitDuration})
                }   
            }
        }
    }

}
