import { modifier_fw_unit_tiny_hero } from "../../modifiers/abilities/unit_innate/modifier_fw_unit_tiny_hero";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_unit_tiny_hero extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_unit_tiny_hero.name
    }

    Precache(context: CScriptPrecacheContext): void {
       
    }
}

