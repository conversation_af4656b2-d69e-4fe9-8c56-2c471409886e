import { modifier_fw_hero_spell_sven } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_sven";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_hero_spell_sven extends BaseAbility{

    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }


    OnSpellStart(): void {
        let hero = this.GetCaster()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_sven/sven_spell_warcry.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))

        StartSoundEventFromPositionReliable("Fw.Hero.Spell.sven.cast", tarPos)
        let team = hero.GetTeam()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType
        let tars = FindUnitsInRadius(
            team,
            tarPos,
            undefined,
            radius,
            tarTeam,
            1 + 2 + 4,
            tarFlag,
            0,
            false,
        )
        if (tars.length > 0) {
            let duration = this.GetSpecialValueFor("duration")
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, this, modifier_fw_hero_spell_sven.name, {duration:duration})
                }
            }
        }
        
    }


    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_hero_spell_sven")

    }

    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/fw_hero_spell_sven/buff/sven_warcry_barrier.vpcf",context)
        PrecacheResource("particle","particles/spell/fw_hero_spell_sven/sven_spell_warcry.vpcf",context)

    }
}

