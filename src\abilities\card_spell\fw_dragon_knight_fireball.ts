import { modifier_fw_fireball_debuff } from "../../modifiers/abilities/card_spell/modifier_fw_fireball_debuff";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

@registerAbility()
export class fw_dragon_knight_fireball extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")
        let first_damage = this.GetSpecialValueFor("first_damage")
        let project_speed = this.GetSpecialValueFor("project_speed")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let damageType = this.GetAbilityDamageType()
        let fwTargetType = this.spell.FWTargetType
        let l = hero.GetAbsOrigin().__sub(tarPos).Length2D()
        project_speed = MathUtils.numInRange(l, 300,project_speed,500,project_speed)
        
        let par2 = ParticleManager.CreateParticle("particles/spell/knight_shard_fireball/snapfire_lizard_blobs_arced.vpcf", ParticleAttachment.WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par2, 0, hero.GetAbsOrigin())
        ParticleManager.SetParticleControl(par2, 1, tarPos)
        ParticleManager.SetParticleControl(par2, 2, Vector(project_speed,0,0))
        hero.EmitSound("Fw.Cards.Spell.fireball.cast")

        let delay = l / project_speed + 0.1
        let par:ParticleID;

        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        Timers.CreateTimer(delay-0.1,()=>{
            e.EmitSound("Fw.Cards.Spell.fireball.impact")
        })
        Timers.CreateTimer(delay,()=>{
            ParticleManager.DestroyParticle(par2, false)
            par = ParticleManager.CreateParticle("particles/spell/knight_shard_fireball/end/warlock_rain_of_chaos_start.vpcf", ParticleAttachment.WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(par, 0, tarPos)
            ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))
            ParticleManager.SetParticleControl(par, 2, Vector(20,0,0))
            hero.StopSound("Fw.Cards.Spell.fireball.cast")
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                UnitTargetType.HERO + UnitTargetType.CREEP + UnitTargetType.BUILDING,
                tarFlag,
                FindOrder.ANY,
                false,
            )
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, ab, modifier_fw_fireball_debuff.name, {duration:duration+1, damageType:damageType}) 
                    ApplyDamage({
                        victim: unit,
                        attacker: hero,
                        damage: first_damage,
                        damage_type: damageType,
                        damage_flags:DamageFlag.NONE,
                        ability:ab,
                    });
                }
            }
            
        })

        Timers.CreateTimer(delay+duration,()=>{
            ParticleManager.DestroyParticle(par, false)
            e.StopSound("Fw.Cards.Spell.fireball.impact")
            GameRules.SoundUtils.backSoundEntity(e)
        })
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_dragon_knight_fireball")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/knight_shard_fireball/snapfire_lizard_blobs_arced.vpcf",context)
        PrecacheResource("particle","particles/spell/knight_shard_fireball/dragon_knight_shard_fireball.vpcf",context)
        PrecacheResource("particle","particles/spell/knight_shard_fireball/end/warlock_rain_of_chaos_start.vpcf",context)
        PrecacheResource("particle","particles/units/heroes/hero_doom_bringer/doom_infernal_blade_debuff.vpcf",context)
        PrecacheResource("particle","particles/status_fx/status_effect_doom.vpcf",context)
    }
}

