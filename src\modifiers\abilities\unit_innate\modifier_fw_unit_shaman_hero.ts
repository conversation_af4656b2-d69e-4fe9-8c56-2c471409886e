import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_unit_shaman_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    extra_range:number;
    extra_dsub:number;
    max_dsub:number;
    OnCreated(params: object): void {
        this.extra_range = this.GetAbility().GetSpecialValueFor("extra_range")
        this.extra_dsub = this.GetAbility().GetSpecialValueFor("extra_dsub")
        this.max_dsub = this.GetAbility().GetSpecialValueFor("max_dsub")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierCastRangeBonus(event: ModifierAbilityEvent): number {
        return this.extra_range * this.GetStackCount()
    }

    GetModifierManacostReduction_Constant(event: ModifierAbilityEvent): number {
        return Math.max(200 - this.extra_dsub * this.GetStackCount(), this.max_dsub)
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.MANACOST_REDUCTION_CONSTANT,
            ModifierFunction.CAST_RANGE_BONUS,
        ];
    }
    
}
