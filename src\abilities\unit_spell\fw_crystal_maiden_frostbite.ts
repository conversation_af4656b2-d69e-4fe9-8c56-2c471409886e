import { modifier_fw_crystal_maiden_frostbite } from "../../modifiers/abilities/unit_spell/modifier_fw_crystal_maiden_frostbite";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_crystal_maiden_frostbite extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_crystal_maiden_frostbite.name
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/status_fx/crystal_maiden_crystal_clone.vpcf",context)
        PrecacheResource("particle","particles/units/heroes/hero_crystalmaiden/maiden_crystal_clone_end.vpcf",context)
        PrecacheResource("particle","particles/spell/crystal_maiden_freezing_field/maiden_frostbite_buff.vpcf",context)
        PrecacheResource("particle","particles/status_fx/status_effect_frost_lich.vpcf",context)
    }
}

