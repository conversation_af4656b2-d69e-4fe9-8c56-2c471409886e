import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_crystal_maiden_frostbite_debuff } from "./modifier_fw_crystal_maiden_frostbite_debuff";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_crystal_maiden_frostbite_death extends BaseModifier {
    
    IsHidden() {
        return true;
    }

    IsDebuff(): boolean {
        return false
    }

    IsPurgable(): boolean {
        return false
    }

    IsPurgeException(): boolean {
        return false
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/crystal_maiden_crystal_clone.vpcf"
    }

    CheckState() {
        return {
            [13]: true,
            [6]: true,
            [10]: true,
            [7]: true,
            [2]: true,
            [8]: true,
        }   
    }
    OnCreated(params: object): void {
        if (IsServer()) {
            this.GetParent().EmitSound("Hero_Crystal.CrystalClone.Cast")
        }
    }

    OnDestroy(): void {
        if (IsServer()) {
            let hero = this.GetParent()
            let team = hero.GetTeam()
            let ab = this.GetAbility()
            let range = ab.GetSpecialValueFor("range")
            let duration = ab.GetSpecialValueFor("duration")
            let tarTeam = ab.GetAbilityTargetTeam()
            let tarFlag = ab.GetAbilityTargetFlags()
            let spellInfo = GameRules.KVUtils.getSpellInfo(ab.GetAbilityName())
            let fwTargetType = spellInfo.FWTargetType
            let tars = FindUnitsInRadius(
                team,
                hero.GetAbsOrigin(),
                undefined,
                range,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                1,
                false,
            )
            if (tars.length > 0) {
                for (const unit of tars) {
                    if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                        unit.AddNewModifier(hero, ab, modifier_fw_crystal_maiden_frostbite_debuff.name, {duration:duration}) 
                    }
                }
            }
            let par = ParticleManager.CreateParticle("particles/units/heroes/hero_crystalmaiden/maiden_crystal_clone_end.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(par, 0, hero.GetAbsOrigin())
            ParticleManager.SetParticleControl(par, 1, Vector(range,range,0))
            hero.StopSound("Hero_Crystal.CrystalClone.Cast")
            hero.EmitSound("Hero_Crystal.CrystalClone.Destroy")
            hero.AddNoDraw()
            hero.ForceKill(false)
        }
    }  
    
    GetMinHealth(): number {
        return 1
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            32,
        ];
    }
    
}
