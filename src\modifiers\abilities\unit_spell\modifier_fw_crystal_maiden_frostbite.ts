import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_crystal_maiden_frostbite_death } from "./modifier_fw_crystal_maiden_frostbite_death";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_crystal_maiden_frostbite extends BaseModifier {
    
    IsHidden() {
        return true;
    }

    IsDebuff(): boolean {
        return false
    }

    IsPurgable(): boolean {
        return false
    }

    IsPurgeException(): boolean {
        return false
    }

    
    OnTakeDamageKillCredit(event: ModifierAttackEvent): void {
        if (event.target.entindex() == this.GetParent().entindex()) {
            if (event.damage > this.GetParent().GetHealth()) {
                if (!this.GetParent().PassivesDisabled()) {
                    this.GetParent().AddNewModifier(this.GetCaster(),this.GetAbility(), modifier_fw_crystal_maiden_frostbite_death.name, {duration:this.GetAbility().GetSpecialValueFor("delay")})
                }
                this.Destroy()
            }
        }
    }
  
    GetMinHealth(): number {
        return 1
    }
    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            32,
            31,
        ];
    }

    RemoveOnDeath(): boolean {
        return true
    }
    
}
