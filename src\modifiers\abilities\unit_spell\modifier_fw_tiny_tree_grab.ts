import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";
import { modifier_fw_unit_tiny_hero } from "../unit_innate/modifier_fw_unit_tiny_hero";


@registerModifier()
export class modifier_fw_tiny_tree_grab extends BaseModifier {
    

    IsHidden() {
        return false;
    }
    
    tree:CBaseEntity;
    num:number;
    extra_num:number;
    attackRangeBonus:number;
    bonus_damage:number;
    splash_width:number;
    splash_range:number;
    splash_pct:number;
    bonus_damage_buildings:number;
    status_resistance:number;
    OnCreated(params: object): void {
        let ab = this.GetAbility()
        let hero = this.GetParent()
        this.num = ab.GetSpecialValueFor("attack_count")
        this.extra_num = ab.GetSpecialValueFor("extra_num")
        if (hero.HasModifier(modifier_fw_unit_tiny_hero.name)) {
            this.num += hero.GetModifierStackCount(modifier_fw_unit_tiny_hero.name, hero) * this.extra_num
        }
        if (IsServer()) {
            let modelName = GameRules.KVUtils.getSpecialUnitInfoTreated(hero.GetUnitName()).ExtraModel
            let qv = MathUtils.getAttachPosWithOffset(hero, "attach_attack2",QAngle(190,10,30),Vector(-20,20,-40), 1.5)
            this.tree = SpawnEntityFromTableSynchronous("prop_dynamic", {
                targetname:"fw_extra_entity_tree_"+DoUniqueString("Tree"),
                model:modelName,
                origin : qv.pos.x+" "+qv.pos.y+" "+qv.pos.z,
                angles : qv.angle.x+" "+qv.angle.y+" "+qv.angle.z,
                scales : "1.5 1.5 1.5",
            })
            this.tree.SetParent(hero, "attach_attack2")
            ab.StartCooldown(1)
            ab.SetActivated(false)
            this.SetStackCount(this.num)
        }
        this.attackRangeBonus = ab.GetSpecialValueFor("attack_range")
        this.bonus_damage = ab.GetSpecialValueFor("bonus_damage")
        this.splash_width = ab.GetSpecialValueFor("splash_width")
        this.splash_range = ab.GetSpecialValueFor("splash_range")
        this.splash_pct = ab.GetSpecialValueFor("splash_pct") / 100
        this.bonus_damage_buildings =  ab.GetSpecialValueFor("bonus_damage_buildings")
        this.status_resistance =  ab.GetSpecialValueFor("status_resistance")
    }

    GetActivityTranslationModifiers(): string {
        return "tree"
    }

    GetAttackSound(): string {
        return "Hero_Tiny_Tree.Attack"
    }

    OnDestroy(): void {
        if (IsServer()) {
            UTIL_Remove(this.tree)
            let ab = this.GetAbility()
            ab.StartCooldown(ab.GetCooldown(1))
            ab.SetActivated(true)
        }
    }

    OnAttackStart(event: ModifierAttackEvent): void {
       if (IsServer() && event.attacker.entindex() == this.GetParent().entindex()) {
            let hero = this.GetParent()
            hero.EmitSound("Hero_Tiny_Tree.PreAttack")
        }
    }

    OnAttackLanded(event: ModifierAttackEvent): void {
        if (event.attacker.entindex() == this.GetParent().entindex()) {
            this.num -= 1
            if (this.num == 0) {
                this.Destroy()
            }
            this.SetStackCount(this.num)

            let ab = this.GetAbility()
            let hero = this.GetParent()
            let tarPos = event.target.GetAbsOrigin()
            let startPos = hero.GetAbsOrigin()
            let v = tarPos.__sub(startPos).Normalized()
            let endPos = v.__mul(this.splash_range).__add(startPos)
            //溅射
            let tars = FindUnitsInRadius(
                hero.GetTeam(),
                tarPos,
                undefined,
                this.splash_range,
                DOTA_UNIT_TARGET_TEAM.DOTA_UNIT_TARGET_TEAM_ENEMY, DOTA_UNIT_TARGET_TYPE.DOTA_UNIT_TARGET_HERO + DOTA_UNIT_TARGET_TYPE.DOTA_UNIT_TARGET_CREEP, DOTA_UNIT_TARGET_FLAGS.DOTA_UNIT_TARGET_FLAG_NONE,
                0,
                false,
            )
            for (const tar of tars) {
                if (tar.entindex() != event.target.entindex() && tar.fastWarUnitType != GoFastWarAIUnitTypeEnum.BUILDING) {
                    ApplyDamage({
                        victim: tar,
                        attacker: hero,
                        damage: event.damage * this.splash_pct,
                        damage_type: DAMAGE_TYPES.DAMAGE_TYPE_PHYSICAL,
                        damage_flags: DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                        ability:ab,
                    });
                    let par = ParticleManager.CreateParticle("particles/units/heroes/hero_tiny/tiny_craggy_cleave.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, tar)
                    ParticleManager.SetParticleControl(par, 0, tar.GetAbsOrigin())
                    ParticleManager.SetParticleControl(par, 1, tar.GetAbsOrigin())
                    ParticleManager.SetParticleControlForward(par, 2, v)
                    ParticleManager.SetParticleControl(par, 2, hero.GetAbsOrigin())
                }
            }
        }
    }

    RemoveOnDeath(): boolean {
        return true
    }

    /**
     * 额外攻击距离
     */
    GetModifierAttackRangeBonus(): number {
        return this.attackRangeBonus
    }

    /**
     * 额外攻击力
     */
    GetModifierPreAttack_BonusDamage(): number {
        return this.bonus_damage
    }

    /**
     * 对建筑伤害加深
     */
    GetModifierTotalDamageOutgoing_Percentage(event: ModifierAttackEvent): number {
        if (event.attacker.entindex() == this.GetParent().entindex()
            && event.target.fastWarUnitType == GoFastWarAIUnitTypeEnum.BUILDING) {
                return this.bonus_damage_buildings
        } else {
            return 0
        }
    }

    GetModifierStatusResistanceStacking(): number {
        return this.status_resistance
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            10,
            7,
            12,
            20,
            21,
            16,
            17,
            18,
        ];
    }
    
}
