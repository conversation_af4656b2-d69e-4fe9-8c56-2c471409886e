import { modifier_fw_huskar_berserkers_blood } from "../../modifiers/abilities/unit_spell/modifier_fw_huskar_berserkers_blood";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_huskar_berserkers_blood extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_huskar_berserkers_blood.name
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/units/heroes/hero_huskar/huskar_berserkers_blood.vpcf",context)
        PrecacheResource("particle","particles/status_fx/status_effect_bloodrage.vpcf",context)
    }
}

