
export class PlayerData {

    private PlayerDataCards:{
        [keys:number]:{
            [keys:string]:{
                dire:string,//夜宴偏好
                radiant:string,//天辉偏好
            }
        }
    } = {}

    public addPlayer (playerID:PlayerID) {
        this.PlayerDataCards[playerID] = {}
    }

    /**
     * 根据玩家数据获取特殊单位
     */
    public getPlayerSpecialCardInfo (playerID:PlayerID, templateName:string , team:DotaTeam) {
        let pData = this.PlayerDataCards[playerID]
        let goodguyFlag = true
        if (GameRules.FastWarPhaseController != undefined && GameRules.FastWarPhaseController.GameTeamGroup[team] != undefined) {
            goodguyFlag = !GameRules.FastWarPhaseController.GameTeamGroup[team].enemyIsGoodGuy
        }
        // print("根据玩家数据获取特殊单位"+playerID+"   templateName:"+templateName+",goodguyFlag:"+goodguyFlag)
        let res = pData[templateName]
        if (res == undefined) {
            res = {
                dire:"",
                radiant:"",
            }
            let u1 = GameRules.KVUtils.getSpecialUnitInfoUntreated("dire_"+templateName)
            res.dire = u1==undefined?("default_"+templateName):("dire_"+templateName)
            let u2 = GameRules.KVUtils.getSpecialUnitInfoUntreated("radiant_"+templateName)
            res.radiant = u2==undefined?("default_"+templateName):("radiant_"+templateName)
            pData[templateName] = res
        }
        return goodguyFlag?res.radiant:res.dire
    }

}