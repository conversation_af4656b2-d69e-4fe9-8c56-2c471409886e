import { modifier_fw_hero_spell_sven } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_sven";
import { modifier_fw_hero_spell_treant } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_treant";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_hero_spell_treant extends BaseAbility{

    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        print("fw_hero_spell_treant")
        let hero = this.GetCaster()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_treant/treant_eyesintheforest.vpcf", ParticleAttachment.WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos.__add(Vector(0,0,15)))
        ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))

        StartSoundEventFromPositionReliable("Fw.Hero.Spell.treant.cast", tarPos)
        let team = hero.GetTeam()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType
        let tars = FindUnitsInRadius(
            team,
            tarPos,
            undefined,
            radius,
            tarTeam,
            UnitTargetType.HERO + UnitTargetType.CREEP + UnitTargetType.BUILDING,
            tarFlag,
            FindOrder.ANY,
            false,
        )
        if (tars.length > 0) {
            let duration = this.GetSpecialValueFor("duration")
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, this, modifier_fw_hero_spell_treant.name, {duration:duration})
                }
            }
        }
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_hero_spell_treant")

    }

    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/units/heroes/hero_treant/treant_livingarmor.vpcf",context)//树甲
        PrecacheResource("particle","particles/spell/fw_hero_spell_treant/treant_eyesintheforest.vpcf",context)

    }
}

