import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_hero_spell_saint extends BaseModifier {
    
    par:ParticleID;
    OnCreated(keys:any): void {
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_saint/silencer_curse.vpcf", ParticleAttachment_t.PATTACH_OVERHEAD_FOLLOW, unit)
            ParticleManager.SetParticleControl(this.par, 0, unit.GetAbsOrigin())
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false);
        }
    }

    IsDebuff(): boolean {
        return true;
    }

    IsHidden() {
        return false;
    }

    IsPurgable(): boolean {
        return true;
    }

    GetTexture () {
        return "drow_ranger_wave_of_silence"
    }

    AllowIllusionDuplicate () {
        return true;
    }

    CheckState() {
        return {
            [15]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

}
