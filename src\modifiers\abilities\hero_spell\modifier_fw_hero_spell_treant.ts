import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_hero_spell_treant extends BaseModifier {
    
    par:ParticleID;
    armor:number;
    health_regen:number;
    OnCreated(params: object): void {
        let unit = this.GetParent()
        let ab = this.GetAbility()
        this.armor = ab.GetSpecialValueFor("armor")
        this.health_regen = ab.GetSpecialValueFor("health")
        if (IsServer()) {
            unit.EmitSound("Fw.Hero.Spell.treant.buff")
            this.par = ParticleManager.CreateParticle("particles/units/heroes/hero_treant/treant_livingarmor.vpcf",ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControlEnt(this.par, 1, unit, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
    }

    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false)
        }
    }
   
    IsDebuff(): boolean {
        return false;
    }

    IsHidden() {
        return false;
    }

    IsPurgable(): boolean {
        return true;
    }
    
    RemoveOnDeath(): boolean {
        return true
    }

    GetTexture(): string {
        return "treant_living_armor"
    }

    GetModifierPhysicalArmorBonus(event: ModifierAttackEvent): number {
        return this.armor
    }

    GetModifierConstantHealthRegen(): number {
        return this.health_regen
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            26,
            33,
        ];
    }
    
}
