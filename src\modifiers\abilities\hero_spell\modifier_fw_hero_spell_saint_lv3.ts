import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_hero_spell_saint_lv3 extends BaseModifier {
    
    par:ParticleID;
    movespeed_slow:number;
    damage:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        this.movespeed_slow = ab.GetSpecialValueFor("movespeed_slow")
        this.damage = ab.GetSpecialValueFor("damage") / 10
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_saint/silencer_curse2.vpcf", ParticleAttachment.OVERHEAD_FOLLOW, unit)
            ParticleManager.SetParticleControl(this.par, 0, unit.GetAbsOrigin())
            this.StartIntervalThink(0.1)
        }
    } 
    OnIntervalThink(): void {
        ApplyDamage({
            victim: this.GetParent(),
            attacker: this.GetCaster(),
            damage: this.damage,
            damage_type: this.GetAbility().GetAbilityDamageType(),
            damage_flags:DamageFlag.NONE,
            ability:this.GetAbility(),
        });
    }

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false);
        }
    }
  
    IsDebuff(): boolean {
        return true;
    }

    IsHidden() {
        return false;
    }

    IsPurgable(): boolean {
        return true;
    }

    GetTexture () {
        return "drow_ranger_silence_arcana_alt1"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [ModifierState.SILENCED]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierMoveSpeedBonus_Percentage(): number {
        return -this.movespeed_slow
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.MOVESPEED_BONUS_PERCENTAGE,
            // ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ];
    }
    
}
