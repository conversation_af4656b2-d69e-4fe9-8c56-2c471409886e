import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_invis } from "./modifier_fw_invis";


@registerModifier()
export class modifier_fw_invis_grad extends BaseModifier {
    

    OnCreated(keys:any): void {
        if (IsServer()) {
            let delay = keys.delay
            this.addNum = Math.round(100 / (delay * 10))
            this.StartIntervalThink(0.1)
            let hero = this.GetParent()
            let par = ParticleManager.CreateParticle("particles/units/heroes/hero_bounty_hunter/bounty_hunter_windwalk.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, hero)
            ParticleManager.SetParticleControlEnt(par, 0, hero, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
    } 

    addNum:number;
    OnIntervalThink(): void {
        let num = this.GetStackCount() + this.addNum
        if (num >= 100) {
            let ab = this.GetAbility()
            this.GetParent().AddNewModifier(this.GetCaster(), ab, modifier_fw_invis.name, {duration:ab.GetSpecialValueFor("duration")})
            this.Destroy()
        }
        this.SetStackCount(num)
    }

    OnDestroy(){
        
    }

    IsHidden() {
        return true;
    }

    IsDebuff(): boolean {
        return false
    }

    IsPurgable(): boolean {
        return true
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierInvisibilityLevel(): number {
        return this.GetStackCount() / 100
    }
  
    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            6,
        ];
    }
    
}
