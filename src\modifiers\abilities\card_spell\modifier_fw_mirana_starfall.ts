import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_mirana_starfall extends BaseModifier {
    
    damage_interval:number;
    damage:number;
    par:ParticleID;
    timer:string;
    OnCreated(keys:any): void {
        if (IsServer()) {
            let victim = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/units/heroes/hero_mirana/mirana_starfall_attack.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, victim)
            ParticleManager.SetParticleControlEnt(this.par, 0, victim, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            victim.EmitSound("Fw.Cards.Spell.starfall.impact")
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            let victim = this.GetParent()
            let ab = this.GetAbility()
            let attacker = ab.GetCaster()
            let damage = ab.GetSpecialValueFor("damage")
            let damageType = ab.GetAbilityDamageType()
            ApplyDamage({
                victim: victim,
                attacker: attacker,
                damage: damage,
                damage_type: damageType,
                damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                ability:ab,
            });
        }
    }

    IsHidden() {
        return true;
    }

    IsPurgable(): boolean {
        return false
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    // GetModifierAttackSpeedBonus_Constant(): number {
    //     return -this.attackspeed_slow
    // }
    
    // GetModifierMoveSpeedBonus_Constant(): number {
    //     return -this.movespeed_slow
    // }


    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            // 1,
            // 2,
            ];
    }
    
}
