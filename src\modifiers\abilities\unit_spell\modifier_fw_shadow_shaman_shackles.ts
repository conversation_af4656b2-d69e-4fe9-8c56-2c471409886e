import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_unit_shaman_hero } from "../unit_innate/modifier_fw_unit_shaman_hero";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_shadow_shaman_shackles extends BaseModifier {
    
    IsHidden() {
        return false;
    }

    IsDebuff(): boolean {
        return true
    }

    IsPurgable(): boolean {
        return true
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/status_effect_shaman_shackle.vpcf"
    }

    StatusEffectPriority(): modifierpriority {
        return ModifierPriority.HIGH
    }

    GetOverrideAnimation(): GameActivity_t {
        return GameActivity.DOTA_DISABLED
    }
    
    CheckState() {
        return {
            [ModifierState.STUNNED]: true,
        }   
    }

    par:ParticleID;
    damage:number;
    self_damage:number;
    damageType:DamageTypes;
    OnCreated(params: object): void {
        if (IsServer()) {
            let tar = this.GetParent()
            let caster = this.GetCaster()
            tar.EmitSound("Hero_ShadowShaman.Shackles")
            this.par = ParticleManager.CreateParticle("particles/units/heroes/hero_shadowshaman/shadowshaman_shackle.vpcf", ParticleAttachment.ABSORIGIN_FOLLOW,tar)
            ParticleManager.SetParticleControlEnt(this.par, 0, caster, ParticleAttachment.POINT_FOLLOW, "attach_hand1", Vector(0,0,0), true)
            ParticleManager.SetParticleControlEnt(this.par, 1, tar, ParticleAttachment.POINT_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControlEnt(this.par, 4, tar, ParticleAttachment.POINT_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControlEnt(this.par, 5, caster, ParticleAttachment.POINT_FOLLOW, "attach_hand2", Vector(0,0,0), true)
            ParticleManager.SetParticleControlEnt(this.par, 6, caster, ParticleAttachment.POINT_FOLLOW, "attach_hand2", Vector(0,0,0), true)
            this.StartIntervalThink(0.1)
            this.damage = this.GetAbility().GetSpecialValueFor("damage") / 10
            this.self_damage = this.GetAbility().GetSpecialValueFor("self_damage")
            if (caster.HasModifier(modifier_fw_unit_shaman_hero.name)) {
                let extra_dsub = this.GetAbility().GetSpecialValueFor("extra_dsub")
                let max_dsub = this.GetAbility().GetSpecialValueFor("max_dsub")
                this.self_damage -= caster.GetModifierStackCount(modifier_fw_unit_shaman_hero.name, caster) * extra_dsub
                this.self_damage = Math.max(this.self_damage, max_dsub)
            }
            this.self_damage = (this.self_damage/100) * this.damage
            this.damageType = this.GetAbility().GetAbilityDamageType()
        }
    }

    OnIntervalThink(): void {
        ApplyDamage({
            victim: this.GetParent(),
            attacker: this.GetCaster(),
            damage: this.damage,
            damage_type: this.damageType,
            damage_flags:DamageFlag.NONE,
            ability:this.GetAbility(),
        });
        ApplyDamage({
            victim: this.GetCaster(),
            attacker: this.GetCaster(),
            damage: this.self_damage,
            damage_type: this.damageType,
            damage_flags:DamageFlag.NONE,
            ability:this.GetAbility(),
        });
    }

    OnDestroy(): void {
        if (IsServer()) {
            this.GetParent().StopSound("Hero_ShadowShaman.Shackles")
            ParticleManager.DestroyParticle(this.par, false)
        }
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.OVERRIDE_ANIMATION,
            ];
    }
    
}
