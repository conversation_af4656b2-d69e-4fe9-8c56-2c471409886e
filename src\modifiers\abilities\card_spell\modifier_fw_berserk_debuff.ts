import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_berserk_debuff extends BaseModifier {
    
    sub_damage:number;
    attack_speed:number;
    move_speed:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        this.sub_damage = ab.GetSpecialValueFor("sub_damage")
        this.attack_speed = ab.GetSpecialValueFor("attack_speed")
        this.move_speed = ab.GetSpecialValueFor("move_speed")
    } 

    OnDestroy(){
        
    }

    IsHidden() {
        if (this.GetParent().HasModifier("modifier_fw_berserk")) {
            return true
        }
        return false;
    }

    IsPurgable(): boolean {
        return true
    }

    IsDebuff(): boolean {
        return true
    }

    GetTexture () {
        return "warlock_hellborn_upheaval"
    }

    AllowIllusionDuplicate () {
        return true;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierIncomingDamage_Percentage(): number {
        if (this.GetParent().HasModifier("modifier_fw_berserk")) {
            return 0
        }
        return this.sub_damage
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        if (this.GetParent().HasModifier("modifier_fw_berserk")) {
            return 0
        }
        return -this.attack_speed
    }

    GetModifierMoveSpeedBonus_Constant(): number {
        if (this.GetParent().HasModifier("modifier_fw_berserk")) {
            return 0
        }
        return -this.move_speed
    }

    GetModifierModelScale(): number {
        if (this.GetParent().HasModifier("modifier_fw_berserk")) {
            return 0
        }
        return -10
    }

    GetModifierModelScaleAnimateTime(): number {
        return 1
    }

    GetModifierModelScaleUseInOutEase(): number {
        return 1
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            0,
            1,
            2,
            3,
            4,
            5,
            ];
    }
}
