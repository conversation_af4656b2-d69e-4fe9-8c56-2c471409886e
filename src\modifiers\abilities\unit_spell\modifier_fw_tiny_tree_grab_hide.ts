import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_tiny_tree_grab_hide extends BaseModifier {
    
    IsHidden() {
        return true;
    }
    
    CheckState() {
        return {
            // [0]: true,
        }   
    }

    GetModifierMoveSpeed_Limit(): number {
        return 1
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return -500
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            15,
            1,
        ];
    }

    RemoveOnDeath(): boolean {
        return true
    }
    
}
