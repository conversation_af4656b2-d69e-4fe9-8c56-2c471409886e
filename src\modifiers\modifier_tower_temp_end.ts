import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_tower_temp_end extends BaseModifier {
    

    GetTexture(): string {
        return "backdoor_protection"
    }
    
    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [ModifierState.DISARMED]: true,
            [ModifierState.STUNNED]: true,
            [ModifierState.CANNOT_BE_MOTION_CONTROLLED]: true,
        }   
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
                // ModifierFunction.ON_DAMAGE_CALCULATED,
            ];
    }

   
    
}
