import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_tower_temp_end extends BaseModifier {
    

    GetTexture(): string {
        return "backdoor_protection"
    }
    
    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [0]: true,
            [10]: true,
            [4]: true,
        }   
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
                // 19,
            ];
    }

   
    
}
