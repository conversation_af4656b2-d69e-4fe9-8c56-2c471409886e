import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_healthbar_pip extends BaseModifier {
    
    num:number;
    OnCreated(): void {
        // print("格子血条："+this.GetCaster().GetUnitName())
        if (IsClient()) {
            let specialUnitInfo = GameRules.KVUtils.getSpecialUnitInfoUntreated(this.GetCaster().GetUnitName())
            if (specialUnitInfo == undefined) {
                this.num = 10
                return 
            }
            this.num = GameRules.KVUtils.getUnitTemplateInfoTreated(specialUnitInfo["Template"]).statusHealth / 10
        }
    } 

    OnDestroy(){

    }

    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    GetModifierHealthBarPips(event: ModifierAttackEvent): number {
        return this.num
    } 

    GetModifierIncomingDamageConstant(event: ModifierAttackEvent): number {
        if (IsClient()) {
            return 0
        } else {
            // print("触发GetModifierIncomingDamageConstant")
            // print(event.attacker.GetUnitName()+",damage:"+event.damage+",GetHealth:"+event.attacker.GetHealth())
            let attackDNum = 10
            if (event.attacker.entindex() == this.GetCaster().entindex()) {
                // print(event.damage)
                return 0
            }
            let specialUnitInfo = GameRules.KVUtils.getSpecialUnitInfoUntreated(event.attacker.GetUnitName())
            if (specialUnitInfo != undefined) {
                attackDNum = GameRules.KVUtils.getUnitTemplateInfoTreated(specialUnitInfo["Template"]).attackHealthPips
            }
            return attackDNum - event.damage
        }
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [14,
            // 21,
            13];
    }

   
    
}
