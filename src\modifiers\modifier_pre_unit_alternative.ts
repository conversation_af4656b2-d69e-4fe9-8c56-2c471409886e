import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_pre_unit_alternative extends BaseModifier {


    // OnCreated(params: object): void {
    //     if (IsClient()) {
            
    //     }
    // }
    // OnRemoved(): void {
    //     if (IsClient()) {
    //         let hero = this.GetParent() as CDOTA_BaseNPC_Hero
            
    //     }
    // }

    GetAttributes(): ModifierAttribute {
        return ModifierAttribute.PERMANENT + ModifierAttribute.IGNORE_INVULNERABLE //+ ModifierAttribute.MULTIPLE
    }

    IsHidden():boolean {
        return true;
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            // [ModifierState.INVISIBLE]: true,
            [ModifierState.CANNOT_BE_MOTION_CONTROLLED]: true,
            [ModifierState.NOT_ON_MINIMAP]: true,
            [ModifierState.STUNNED]: true,
            [ModifierState.INVULNERABLE]: true,
            // [ModifierState.NO_UNIT_COLLISION]: true,
            [ModifierState.OUT_OF_GAME]: true,
            // [ModifierState.NO_HEALTH_BAR]: true,
          }
        
        return state
    }


}
