
import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_unit_card_duration extends BaseModifier {


    GetAttributes(): ModifierAttribute {
        return ModifierAttribute.PERMANENT + ModifierAttribute.IGNORE_INVULNERABLE //+ ModifierAttribute.MULTIPLE
    }

    IsHidden():boolean {
        return true;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            // [ModifierState.STUNNED]: true,
          }
        return state
    }

    // GetStatusEffectName(): string {
        // return "particles/status_fx/status_effect_faceless_chronosphere.vpcf";
    // }
    
    dNum:number;
    OnCreated(keys: any): void {
        if (IsServer()) {
            let duration = keys.fastWarDuration
            this.dNum = this.GetCaster().GetMaxHealth() / (duration/0.1)
            // print("创建持续扣血:"+ this.dNum)
            this.StartIntervalThink(0.1);
        }
    } 
    OnIntervalThink(): void {
        if (IsServer()) {
            if (!this.GetCaster().IsAlive()) {
                return 
            }
            ApplyDamage({
                victim: this.GetCaster(),
                attacker: this.GetCaster(),
                damage: this.dNum,
                damage_type: DamageTypes.PURE,
                damage_flags:DamageFlag.NONE,
            });
            // print("扣血:"+ this.dNum)
        }
    }

    OnDestroy(){
        if (IsServer()) {
            
        }
    }

}
