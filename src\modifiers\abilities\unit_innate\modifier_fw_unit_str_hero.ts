import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_unit_str_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    damage:number;
    OnCreated(params: object): void {
        this.damage = this.GetAbility().GetSpecialValueFor("damage")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }
    GetModifierPreAttack_BonusDamage(): number {
        return this.GetStackCount() * this.damage
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.PREATTACK_BONUS_DAMAGE,
        ];
    }
    
}
