import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_unit_tiny_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    extra_num:number;
    OnCreated(params: object): void {
        this.extra_num = this.GetAbility().GetSpecialValueFor("extra_num")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierManacostReduction_Constant(event: ModifierAbilityEvent): number {
        return this.extra_num * this.GetStackCount()
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.MANACOST_REDUCTION_CONSTANT,
        ];
    }
    
}
