
import { BaseModifier, registerModifier } from "../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_unit_entrance_default extends BaseModifier {


    GetAttributes(): ModifierAttribute {
        return 1 + 2 //+ 4
    }

    IsHidden():boolean {
        return true;
    }

    GetOverrideAnimation() {
        return GameActivity_t.ACT_DOTA_SPAWN;
    }

    maxLayTime:number;
    pFlag = false;
    GetVisualZDelta(): number {
        // print("GetVisualZDelta")
        // if (this.num > this.maxLayTime) {
        //     if (!this.pFlag) {
        //         let hero = this.GetParent() as CDOTA_BaseNPC_Hero;
        //         let p = ParticleManager.CreateParticle("particles/gui/choose_card/extra/default.vpcf",  ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, hero)
        //         ParticleManager.SetParticleControl(p, 0, Vector(50,0,0))
        //         this.pFlag = true
        //     }
            return 0
        // } else {
        //     return (1-this.num/this.maxLayTime)*100
        // }
    }

    DeclareFunctions(): ModifierFunction[] {
        return [9,
            11];
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            [10]: true,
            // [15]: true,
            // [3]: true,
            // [0]: true,
          }
        
        return state
    }

    // GetStatusEffectName(): string {
        // return "particles/status_fx/status_effect_faceless_chronosphere.vpcf";
    // }
    
    
    unitDuration:number;
    OnCreated(keys: any): void {
        if (IsServer()) {
            this.unitDuration = keys.unitDuration
        } else {
            // print('客户端延迟modifier')
            this.maxLayTime = Math.min(this.GetDuration(), 0.1)
            let hero = this.GetParent() as CDOTA_BaseNPC_Hero;


            
            this.StartIntervalThink(0.03);
        }
    } 

    OnDestroy(){
        if (IsServer()) {

        } else {
           
        }
    }

}
