
import { BaseModifier, registerModifier } from "../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_unit_entrance_default extends BaseModifier {


    GetAttributes(): ModifierAttribute {
        return ModifierAttribute.PERMANENT + ModifierAttribute.IGNORE_INVULNERABLE //+ ModifierAttribute.MULTIPLE
    }

    IsHidden():boolean {
        return true;
    }

    GetOverrideAnimation() {
        return GameActivity.DOTA_SPAWN;
    }

    maxLayTime:number;
    pFlag = false;
    GetVisualZDelta(): number {
        // print("GetVisualZDelta")
        // if (this.num > this.maxLayTime) {
        //     if (!this.pFlag) {
        //         let hero = this.GetParent() as CDOTA_BaseNPC_Hero;
        //         let p = ParticleManager.CreateParticle("particles/gui/choose_card/extra/default.vpcf",  ParticleAttachment.ABSORIGIN_FOLLOW, hero)
        //         ParticleManager.SetParticleControl(p, 0, Vector(50,0,0))
        //         this.pFlag = true
        //     }
            return 0
        // } else {
        //     return (1-this.num/this.maxLayTime)*100
        // }
    }

    DeclareFunctions(): ModifierFunction[] {
        return [ModifierFunction.OVERRIDE_ANIMATION,
            ModifierFunction.VISUAL_Z_DELTA];
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            [ModifierState.STUNNED]: true,
            // [ModifierState.SILENCED]: true,
            // [ModifierState.ROOTED]: true,
            // [ModifierState.DISARMED]: true,
          }
        
        return state
    }

    // GetStatusEffectName(): string {
        // return "particles/status_fx/status_effect_faceless_chronosphere.vpcf";
    // }
    
    
    unitDuration:number;
    OnCreated(keys: any): void {
        if (IsServer()) {
            this.unitDuration = keys.unitDuration
        } else {
            // print('客户端延迟modifier')
            this.maxLayTime = Math.min(this.GetDuration(), 0.1)
            let hero = this.GetParent() as CDOTA_BaseNPC_Hero;


            
            this.StartIntervalThink(0.03);
        }
    } 

    OnDestroy(){
        if (IsServer()) {

        } else {
           
        }
    }

}
