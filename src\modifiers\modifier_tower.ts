import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_tower extends BaseModifier {
    
    num:number;
    OnCreated(keys:any): void {
        if (IsServer()) {
            this.num = keys.bounsRange-100
        } 
    } 

    OnDestroy(){

    }

    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [ModifierState.INVULNERABLE]: true,
        }   
    }

    GetModifierAttackRangeBonus(): number {
        return this.num
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.ATTACK_RANGE_BONUS,
            // ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ];
    }

   
    
}
