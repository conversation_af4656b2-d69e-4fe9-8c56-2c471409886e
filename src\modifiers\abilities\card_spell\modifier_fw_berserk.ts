import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_berserk extends BaseModifier {
    
    sub_damage:number;
    attack_speed:number;
    move_speed:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        this.sub_damage = ab.GetSpecialValueFor("sub_damage")
        this.attack_speed = ab.GetSpecialValueFor("attack_speed")
        this.move_speed = ab.GetSpecialValueFor("move_speed")
    } 

    OnDestroy(){
        
    }

    IsHidden() {
        return false;
    }

    IsPurgable(): boolean {
        return true
    }

    IsDebuff(): boolean {
        return false
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/status_effect_bloodrage.vpcf"
    }

    GetTexture () {
        return "warlock_hellborn_upheaval"
    }

    AllowIllusionDuplicate () {
        return true;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierIncomingDamage_Percentage(): number {
        return -this.sub_damage
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return this.attack_speed
    }

    GetModifierMoveSpeedBonus_Constant(): number {
        return this.move_speed
    }

    GetModifierModelScale(): number {
        return 10
    }

    GetModifierModelScaleAnimateTime(): number {
        return 1
    }

    GetModifierModelScaleUseInOutEase(): number {
        return 1
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.INCOMING_DAMAGE_PERCENTAGE,
            ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ModifierFunction.MODEL_SCALE,
            ModifierFunction.MODEL_SCALE_ANIMATE_TIME,
            ModifierFunction.MODEL_SCALE_USE_IN_OUT_EASE,
            ];
    }
    
}
