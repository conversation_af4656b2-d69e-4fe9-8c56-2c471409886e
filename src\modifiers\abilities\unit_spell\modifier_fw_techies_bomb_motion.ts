import { BaseModifier, BaseModifierMotionBoth, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_techies_bomb_motion extends BaseModifierMotionBoth {
    
    IsHidden() {
        return true;
    }

    IsPurgable(): boolean {
        return false
    }

    IsPurgeException(): boolean {
        return false
    }

    RemoveOnDeath(): boolean {
        return true
    }

    speed:number = 800
    zSpeed:number = 30
    tarUnit:CDOTA_BaseNPC
    OnCreated(keys:any): void {
        if (IsServer()) {
            this.tarUnit = EntIndexToHScript(keys.tarEnt as EntityIndex) as CDOTA_BaseNPC;
            this.SetPriority(modifierpriority.MODIFIER_PRIORITY_NORMAL)
            if(this.ApplyHorizontalMotionController()==false){
                this.Destroy();
            }
            if(this.ApplyVerticalMotionController()==false){
                this.Destroy();
            }
        }
    }
    /**更新水平向位置 */
    UpdateHorizontalMotion (me: CDOTA_BaseNPC, dt: number) {
        let nowPos = me.GetAbsOrigin()
        let tarPos = this.tarUnit.GetAbsOrigin()
        if (nowPos.__sub(tarPos).Length2D() < this.speed*dt) {
            this.Destroy()
            return
        }
        let d = tarPos.__sub(nowPos).Normalized()
        let q = VectorToAngles(d)
        me.SetAbsOrigin(nowPos.__add(d.__mul(this.speed).__mul(dt)))
        me.SetLocalAngles(q.x, q.y,q.z)
    };
    OnHorizontalMotionInterrupted = () => {
        this.Destroy();
    };
    /**更新纵向位置 */
    UpdateVerticalMotion (me: CDOTA_BaseNPC, dt: number) {
        return;
    };
    OnVerticalMotionInterrupted = () => {
        this.Destroy();
    };

    OnDestroy () {
        if (!IsServer()) return;
        this.GetParent().RemoveHorizontalMotionController(this);
        this.GetParent().RemoveVerticalMotionController(this);
        let hero = this.GetParent()
        let ab = this.GetAbility()
        let damage = ab.GetSpecialValueFor("damage")
        let range = ab.GetSpecialValueFor("range")

        let team = hero.GetTeam()
        let tarTeam = ab.GetAbilityTargetTeam()
        let tarFlag = ab.GetAbilityTargetFlags()
        let damageType = ab.GetAbilityDamageType()
        let spellInfo = GameRules.KVUtils.getSpellInfo(ab.GetAbilityName())
        let fwTargetType = spellInfo.FWTargetType
        let tars = FindUnitsInRadius(
            team,
            hero.GetAbsOrigin(),
            undefined,
            range,
            tarTeam,
            1 + 2 + 4,
            tarFlag,
            0,
            false,
        )
        if (tars.length > 0) {
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    ApplyDamage({
                        victim: unit,
                        attacker: hero,
                        damage: damage,
                        damage_type: damageType,
                        damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
                        ability:ab,
                    });
                }
            }
        }
        let par = ParticleManager.CreateParticle("particles/units/heroes/hero_techies/techies_remote_cart_explode.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par,0,hero.GetAbsOrigin())
        ParticleManager.SetParticleControl(par,1,Vector(0,0,range))
        hero.AddNoDraw()
        ApplyDamage({
            victim: hero,
            attacker: hero,
            damage: hero.GetHealth(),
            damage_type: DAMAGE_TYPES.DAMAGE_TYPE_PURE,
            damage_flags:DOTADamageFlag_t.DOTA_DAMAGE_FLAG_NONE,
            ability:ab,
        });
    };

    CheckState() {
        return {
            [1]: true,
            [2]: true,
        }   
    }

}
