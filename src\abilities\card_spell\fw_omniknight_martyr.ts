import { modifier_fw_omniknight_martyr } from "../../modifiers/abilities/card_spell/modifier_fw_omniknight_martyr";
import { modifier_fw_time_travel } from "../../modifiers/abilities/card_spell/modifier_fw_time_travel";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_omniknight_martyr extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let duration = this.GetSpecialValueFor("duration")

        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType
        let e = GameRules.SoundUtils.getSoundEntity(tarPos)
        e.EmitSound("Fw.Cards.Spell.omniknight_martyr.cast")
        Timers.CreateTimer(3,()=>{
            e.StopSound("Fw.Cards.Spell.omniknight_martyr.cast")
            GameRules.SoundUtils.backSoundEntity(e)
        })
        
        let par = ParticleManager.CreateParticle("particles/spell/omniknight_martyr/omni_2021_immortal.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControlForward(par, 0, Vector(0,hero.fastWarIsGood?100:-100,0))
        ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))

        let tars = FindUnitsInRadius(
            team,
            tarPos,
            undefined,
            radius,
            tarTeam,
            1 + 2 + 4,
            tarFlag,
            0,
            false,
        )
        for (const unit of tars) {
            if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                unit.AddNewModifier(hero, ab, modifier_fw_omniknight_martyr.name, {duration:duration}) 
            }
        }
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_omniknight_martyr")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/omniknight_martyr/omni_2021_immortal.vpcf",context)
        PrecacheResource("particle","particles/spell/omniknight_martyr/buff.vpcf",context)
    }
}

