import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_zombie_extra_debuff extends BaseModifier {
    
    OnCreated(keys:any): void {
        if (IsServer()) {
            // print("小僵尸传播瘟疫！")
        }
    } 

    IsHidden() {
        return false;
    }
    
    CheckState() {
        return {
            // [ModifierState.INVULNERABLE]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            // ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            // ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ];
    }
    
}
