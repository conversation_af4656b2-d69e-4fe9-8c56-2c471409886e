import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_time_travel extends BaseModifier {


    exModel:CBaseEntity;
    exV:Vector;
    OnCreated(params: any): void {
        if (IsServer()) {
            let posX = params.posX
            let posY = params.posY
            let hero = this.GetParent()
            // let par = ParticleManager.CreateParticle("particles/spell/time_travel/travel_start_alter.vpcf", ParticleAttachment.ABSORIGIN_FOLLOW, undefined)
            // ParticleManager.SetParticleControlEnt(par, 0 ,hero, ParticleAttachment.ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            // ParticleManager.SetParticleControl(par, 1 ,GetGroundPosition(Vector(posX, posY, 0), hero))
            let ex = GameRules.KVUtils.getSpecialUnitInfoTreated(hero.GetUnitName()).ExtraModel
            for (const v of hero.GetChildren()) {
                if (v.GetModelName() == ex) {
                    this.exModel = v
                }
            }
            if (this.exModel != undefined) {
                this.exV = this.exModel.GetAbsOrigin()
                this.exModel.SetAbsOrigin(Vector(0,0,-200));
            }
            hero.AddNoDraw()
        }
    }
    OnDestroy(): void {
        if (IsServer()) {
            if (this.exModel != undefined) {
                this.exModel.SetAbsOrigin(this.exV);
            }
            this.GetParent().RemoveNoDraw()
        }
    }

    GetAttributes(): ModifierAttribute {
        return ModifierAttribute.PERMANENT  //+ ModifierAttribute.MULTIPLE
    }
    IsHidden():boolean {
        return false;
    }
    RemoveOnDeath(): boolean {
        return true;
    }
    IsDebuff(): boolean {
        return false;
    }
    IsPurgable(): boolean {
        return false
    }
    IsPurgeException(): boolean {
        return false
    }

    CheckState():Partial<Record<modifierstate, boolean>> {
        let state = {
            [ModifierState.ROOTED]: true,
            [ModifierState.FROZEN]: true,
            [ModifierState.CANNOT_BE_MOTION_CONTROLLED]: true,
            [ModifierState.NOT_ON_MINIMAP]: true,
            [ModifierState.STUNNED]: true,
            [ModifierState.INVULNERABLE]: true,
            [ModifierState.MAGIC_IMMUNE]: true,
            [ModifierState.ATTACK_IMMUNE]: true,
            [ModifierState.DEBUFF_IMMUNE]: true,
            [ModifierState.NO_UNIT_COLLISION]: true,
            [ModifierState.OUT_OF_GAME]: true,
            [ModifierState.NO_HEALTH_BAR]: true,
          }
        
        return state
    }


}
