import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";


@registerModifier()
export class modifier_fw_hero_spell_blacksmith extends BaseModifier {

    par:ParticleID;
    attack_speed:number;
    attack_speed_debuff:number;
    attack_damage:number;
    max_num:number;
    current_num:number;
    time:number;
    re_time:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_blacksmith/tower_buff.vpcf",ParticleAttachment.ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControl(this.par, 0, unit.GetAbsOrigin())
            ParticleManager.SetParticleControl(this.par, 1, Vector(10,0,0))
            this.SetHasCustomTransmitterData(true)
            this.StartIntervalThink(0.3)
        }
        this.attack_speed = ab.GetSpecialValueFor("attack_speed")
        this.attack_speed_debuff = ab.GetSpecialValueFor("attack_speed_debuff")
        this.attack_damage = ab.GetSpecialValueFor("attack_damage")
        this.max_num = ab.GetSpecialValueFor("num")
        this.current_num = ab.GetSpecialValueFor("num")
        this.time = ab.GetSpecialValueFor("time")
        this.re_time = ab.GetSpecialValueFor("re_time")
    } 

    lastReTime:number = 0
    OnIntervalThink(): void {
        let nowTime = GameRules.GetGameTime()
        if (this.current_num < this.max_num) {
            if (nowTime - this.lastAttackTime >= this.re_time) {
                if (nowTime - this.lastReTime >= this.time) {
                    this.current_num = Math.min(this.current_num + 1, this.max_num)
                    this.lastReTime = nowTime
                    ParticleManager.SetParticleControl(this.par, 1, Vector(this.current_num,0,0))
                    this.SendBuffRefreshToClients()
                }
            } else {
                this.lastReTime = nowTime
            }
        } else {
            this.lastReTime = nowTime
        }
    }

    AddCustomTransmitterData () {
        return {
            max_num:this.max_num,
            current_num:this.current_num
        }
    }
    
    HandleCustomTransmitterData (data) {
        this.max_num = data.max_num
        this.current_num = data.current_num
    }

    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par,true)
        }
    }

    IsDebuff(): boolean {
        if (this.current_num > 0) {
            return false
        } else {
            return true
        }
    }

    IsHidden() {
         return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        if (this.current_num > 0) {
            return this.attack_speed
        } else {
            return this.attack_speed_debuff
        }
    }
    GetModifierPreAttack_BonusDamage(): number {
        return this.attack_damage
    }

    GetModifierIncomingDamageConstant(event: ModifierAttackEvent): number {
        if (IsClient()) {
            return event.report_max ? this.max_num : this.current_num
        } else {
            return 0
        }
    }

    lastAttackTime:number = 0;
    OnAttack(event: ModifierAttackEvent): void {
        if (event.attacker.entindex() == this.GetParent().entindex()) {
            this.lastAttackTime = GameRules.GetGameTime()
            this.current_num = Math.max(0,this.current_num - 1)
            ParticleManager.SetParticleControl(this.par, 1, Vector(this.current_num,0,0))
            this.SendBuffRefreshToClients()
        }
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            ModifierFunction.PREATTACK_BONUS_DAMAGE,
            ModifierFunction.INCOMING_DAMAGE_CONSTANT,
            ModifierFunction.ON_ATTACK,
        ];
    }
    
}
