import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_zombie_extra extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    duration:number;
    OnCreated(params: object): void {
        if (IsServer()) {
            let ab = this.GetAbility()
            this.duration = ab.GetSpecialValueFor("duration")
        }
    }

    OnAttackLanded(event: ModifierAttackEvent): void {
        if (IsServer()) {
            if (event.attacker.entindex() == this.GetParent().entindex()) {
                // print("小僵尸攻击！给敌人添加负面buff")
                event.target.AddNewModifier(this.GetParent(), this.GetAbility(), modifier_fw_zombie_extra_debuff.name, {duration:this.duration})
            }
        }
    }

    CheckState() {
        return {
            // [6]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            21,
            // 2,
        ];
    }
    
}
