import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_unit_str_hero_health extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    health:number;
    OnCreated(params: object): void {
        this.health = this.GetAbility().GetSpecialValueFor("health")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }
    GetModifierExtraHealthBonus(): number {
        return this.GetStackCount() * this.health
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            24,
        ];
    }
    
}
