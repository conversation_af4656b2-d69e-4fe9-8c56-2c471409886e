import { modifier_fw_berserk_debuff } from "../modifiers/abilities/card_spell/modifier_fw_berserk_debuff";
import { modifier_fw_luna_eclipse } from "../modifiers/abilities/card_spell/modifier_fw_luna_eclipse";
import { modifier_attack_speed } from "../modifiers/modifier_attack_speed";
import { modifier_player_hero } from "../modifiers/modifier_player_hero";
import { modifier_pre_unit_alternative } from "../modifiers/modifier_pre_unit_alternative";
import { modifier_test } from "../modifiers/modifier_test";
import { AIController } from "./fastwarai"; 
import { FastWarCard } from "./fastwarcard";
import { FastWarGamePhaseController } from "./fastwargamephasecontroller";
import { Test } from "./test";

export class GameMode { 

    constructor() {
        if (IsInToolsMode()) {
            Test.test()
        }
        ListenToGameEvent("npc_spawned", event => this.OnNpcSpawned(event), undefined);
        ListenToGameEvent("dota_player_pick_hero", (e) => this.playerPickHero(e), undefined);
        ListenToGameEvent("game_rules_state_change", () => this.OnStateChange(), undefined);
        ListenToGameEvent("dota_tower_kill", (e) => this.OnTowerDie(e), undefined);
        ListenToGameEvent("entity_killed", (e) => this.OnEntityDie(e), undefined);
        CustomGameEventManager.RegisterListener("go_used_card", (_, data) => {
            print("后台接收：使用卡牌："+data.cardId)   
            if (data.pid == GameRules.BotPlayerId && !GameRules.DebugOpen.debugMana) {
                Say(HeroList.GetHero(0), "只有在开启了debugMana的情况下才能操控AI出牌", false);
            } 
            GameRules.FastWarCard.useCard(data.pid as PlayerID, data.cardId, GetGroundPosition(Vector(data.aim.x,data.aim.y,data.aim.z), undefined),false,data.isHeroSpell == 1)
        });

        CustomGameEventManager.RegisterListener("go_change_deck_card", (_, data) => {
            // print("后台接收：调换卡牌："+data.cardId)    
            if (data.deckToReserve == 0) {
                GameRules.FastWarCard.changeReserveCardToDeckNetTable(data.pid as PlayerID, data.cardId)
            } else {
                GameRules.FastWarCard.changeDeckCardToReserveNetTable(data.pid as PlayerID, data.cardId)
            }
        });
        
        CustomGameEventManager.RegisterListener("go_random_deck_card", (_, data) => {
            if (data.deckOrHero == 1) {
                GameRules.FastWarCard.completionDeck([data.pid as PlayerID], true, 0)
            } else {
                GameRules.FastWarPlayerHeroSpell.randomPlayerHero(data.pid as PlayerID)
            }
        });
        
        CustomGameEventManager.RegisterListener("go_ui_choose_mode", (_, data) => {
            if (!GameRules.PlayerHasCustomGameHostPrivileges(PlayerResource.GetPlayer(data.PlayerID))) {
                Say(HeroList.GetHero(0), "非房主不能选择游戏模式！", false);
            }
            GameRules.FastWarPhaseController.chooseMode(data.groupIndex, data.modeIndex)
        });

        CustomGameEventManager.RegisterListener("go_change_player_hero", (_, data) => {
            GameRules.FastWarPlayerHeroSpell.changePlayerHero(data.pid as PlayerID, data.heroName)
        });

        CustomGameEventManager.RegisterListener("go_test", (_, data) => {
            GameRules.Debug.DebugPrint(data.str)
        });

        CustomGameEventManager.RegisterListener("go_game_start", (_, data) => {
            // print("测试接口！")
            // DeepPrintTable(data)
            data.pid = parseInt(data.pid.toString())
            if (data.status == 1) {
                if (GameRules.testPlayer == -1) {
                    if (GameRules.playerNum >= 2) {
                        GameRules.testPlayer = data.pid
                        CustomGameEventManager.Send_ServerToAllClients("go_game_start",{pid:data.pid,status:1})
                    } else {
                        //开始AI对战
                        if (GameRules.testAIT != undefined) {
                            Timers.RemoveTimer(GameRules.testAIT)
                        }
                        GameRules.testAI?.DestroyThink()
                        GameRules.FastWarCard.completionDeck([GameRules.BotPlayerId], false, 1)

                        let playerPID = GameRules.PlayerIDs[0]
                        if (playerPID == GameRules.BotPlayerId) {
                            playerPID = GameRules.PlayerIDs[1]
                        }
                        GameRules.testAIT = Timers.CreateTimer(8,()=>{
                            GameRules.testAI = new AIController(GameRules.BotPlayerId,playerPID)
                            GameRules.testAI.initAI()
                        })
                        GameRules.FastWarPhaseController.startTestGame(playerPID, GameRules.BotPlayerId)
                    }
                } else {
                    if (GameRules.testPlayer == data.pid) {
                        CustomGameEventManager.Send_ServerToAllClients("go_game_start",{pid:data.pid,status:1})
                    } else {
                        GameRules.FastWarPhaseController.startTestGame(GameRules.testPlayer as PlayerID, data.pid as PlayerID)
                    }
                }
            } else if (data.status == 2) {
                if (GameRules.testPlayer != -1) {
                    GameRules.FastWarPhaseController.startTestGame(GameRules.testPlayer as PlayerID, data.pid as PlayerID)
                    GameRules.testPlayer = -1
                }
            } else if (data.status == 3) {
                GameRules.testPlayer = -1
            }
        });
        
    }
    
    private OnNpcSpawned(event: NpcSpawnedEvent) {
        const unit = EntIndexToHScript(event.entindex) as CDOTA_BaseNPC_Hero; // 强制转换为npc，因为这是'npc_spawn '事件
        // print("创建单位："+unit.GetUnitName()+",id为："+unit.entindex()+",BaseAttackSpeed:"+unit.GetAttackSpeed(true))
        unit.AddNewModifier(unit, undefined, modifier_attack_speed.name, {})
        
        unit.AddAbility("generic_hidden")
        unit.AddAbility("generic_hidden")
        unit.AddAbility("generic_hidden")
        unit.AddAbility("generic_hidden")
        unit.AddAbility("generic_hidden")
        unit.GetAbilityByIndex(0)?.SetLevel(1)
        unit.GetAbilityByIndex(1)?.SetLevel(1)
        unit.GetAbilityByIndex(2)?.SetLevel(1)
        unit.GetAbilityByIndex(3)?.SetLevel(1)
        unit.GetAbilityByIndex(4)?.SetLevel(1)
        if (unit.GetModelName()== "models/pets/armadillo/armadillo.vmdl") {
            unit.AddNoDraw()
        }
        // unit.AddNewModifier(undefined, undefined, modifier_test.name, {})
    }

    private playerPickHero(event:DotaPlayerPickHeroEvent) {
        // DeepPrintTable(event)
        let player = EntIndexToHScript(event.player as EntityIndex) as CDOTAPlayerController;
        let hero = EntIndexToHScript(event.heroindex as EntityIndex) as CDOTA_BaseNPC_Hero;
        if (event.player >= 0 && player != null && player != undefined) {
            // print("dotago:playerPickHero："+player.GetPlayerID())
            let pid = player.GetPlayerID()
            if (GameRules.PlayerIDs.indexOf(pid) == -1) {
                GameRules.PlayerIDs.push(pid)
                GameRules.playerNum += 1
                GameRules.PlayerData.addPlayer(pid)
                GameRules.FastWarCard.initDeckCardNetTable(pid)
                GameRules.FastWarPlayerHeroSpell.initPlayerHeroNetTable(pid)
                AddFOWViewer(player.GetTeam(), Vector(0,0,0), 3000, 9999, false)
                
                let playerInfo = CustomNetTables.GetTableValue("game_player_info", "player")
                if (playerInfo == undefined) {
                    playerInfo = {}
                }
                playerInfo[pid.toString()] = {
                    isPlayer:GameRules.BotFlag?0:1,
                    uiPID:pid,
                    enemyUIPID:pid==0?1:0,
                    uiPIsGood:player.GetTeam() == DotaTeam.GOODGUYS?1:0,
                    uiPHero:hero.entindex(),
                }
                CustomNetTables.SetTableValue("game_player_info", "player", playerInfo)
                if (GameRules.BotFlag) {
                    GameRules.BotPlayerId = pid
                    GameRules.BotFlag = false
                }
                Timers.CreateTimer(0.03,()=>{
                    let ps = GameRules.EntityUtils.EntityOnlyPos["tower"]
                    hero.SetAbsOrigin(ps[(player.GetTeam() == DotaTeam.GOODGUYS)?"goodguys3":"badguys3"].pos.__add(Vector(0,0,500)))
                    hero.AddNoDraw()
                    hero.AddNewModifier(undefined, undefined, modifier_player_hero.name, {})
                    hero.fastWarIsGood = (player.GetTeam() == DotaTeam.GOODGUYS)
                })
                return
            }
        }
        if (hero != undefined) {
            Timers.CreateTimer(0.03,()=>{
                // hero.AddNoDraw()
                hero.AddNewModifier(undefined, undefined, modifier_pre_unit_alternative.name, {})
            })
            // print("dotago:playerPickHero_heroName："+hero.GetUnitName())
        }
    }

    private OnEntityDie(event:EntityKilledEvent) {
        let unit = EntIndexToHScript(event.entindex_killed as EntityIndex) as CDOTA_BaseNPC;
        // print("dotago:OnEntityDie,unitName:"+unit.GetUnitName()+",className:"+unit.GetClassname()+",towerIndex:"+unit.towerIndex)
        if (unit.towerIndex == 1 || unit.towerIndex == 2 || unit.towerIndex == 3) {
            if (GameRules.FastWarPhaseController != undefined) {
                //塔被摧毁
                let info = GameRules.FastWarPhaseController.GameTeamGroup[unit.GetTeam()]
                let team = info.enemyTeamNumber
                let isGood = info.enemyIsGoodGuy
                let tInfo =  GameRules.FastWarPhaseController.GameTeamGroup[team]
                tInfo.enemyTower[unit.towerIndex-1] = 0
                GameRules.FastWarPhaseController.GameTeamGroup[team] = tInfo
                GameRules.FastWarPhaseController.emit(FastWarGameEvent.TowerDestroyed, {towerIndex:unit.towerIndex, enemyIsGood:isGood, enemyPID:info.enemyPlayer, pid:unit.towerPlayerId})   
                GameRules.NPCUtils.sendMessageToUnits({type:GoFastWarAIMessageTypeEnum.SET_UNIT_TAR,data:{}},undefined,team) 
                if (unit.towerIndex == 1 || unit.towerIndex == 2) {
                    GameRules.NPCUtils.activeAcient(unit.GetTeam())
                }
            }   
        }
    }

    private OnTowerDie(event:DotaTowerKillEvent) {
        print("dotago:OnTowerDie")
    }

    private OnStateChange() {
        const state = GameRules.State_Get();
        print("fastWar:游戏进度" + state)
        // 游戏开始前一次点击
        if (state === GameState.PRE_GAME) {
            Timers.CreateTimer(0.2, () => {
                CustomNetTables.SetTableValue("fast_war_game_phase","fast_war_game_mode", {
                    nowGameGroup:GoFastWarGameModeEnum.None,
                    nowGameMode:0,
                    group:GoFastWarGameModeEnum.None,
                    mode:0
                });
            });
        }
        if (state === GameState.HERO_SELECTION) {
            if (GameRules.PlayerIDs.length < 2) {
                Tutorial.AddBot("npc_dota_hero_wisp", "", "", false)
                GameRules.playerNum -= 1
                GameRules.BotFlag = true
                Timers.CreateTimer(3,()=>{
                    Say(HeroList.GetHero(0), "现在可以使用tab切换队伍来为AI选择出战卡组~", false);
                })
            }
        }
        if (state === GameState.CUSTOM_GAME_SETUP) {
            print("玩家就绪阶段")
            // let keys: { [key: string]: string; } = {};
            // for (let i = 0; i < 50; ++i) {
            //     let key = `key_${i}`;
            //     keys[key] = GetDedicatedServerKeyV2(key);
            // }
            // CustomNetTables.SetTableValue(`keys`, `keys`, keys);
        }
    }
}
