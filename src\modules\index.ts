import { EntityUtils } from '../utils/entityutils';
import { KVUtils } from '../utils/kvutils';
import { SoundUtils } from '../utils/sound_utils';
import { Debug } from './debug';
import { AIController } from './fastwarai';
import { FastWarCard } from './fastwarcard';
import { FastWarGamePhaseController } from './fastwargamephasecontroller';
import { FastWarPlayerHeroSpell } from './fastwarplayerherospell';
import { FastWarSpell } from './fastwarspell';
import { GameConfig } from './gameconfig';
import { GameMode } from './gamemode';
import { NPCUtils } from './NPCUtils';
import { PlayerData } from './playerdata';
import { XNetTable } from './xnet-table';

declare global {
    interface CDOTAGameRules {
        DebugOpen:{
            debugPrint:boolean,
            debugMana:boolean,
            debugDamage:number,
        }
        testAI:AIController;
        testAIT:string;
        testNPC:CDOTA_BaseNPC;
        testPlayer:number;
        fastWarGameStatus:FastWarGamePhase,
        PlayerIDs:PlayerID[],
        playerNum:number,
        BotPlayerId:PlayerID,
        BotFlag:boolean,

        // 声明所有的GameRules模块，这个主要是为了方便其他地方的引用（保证单例模式）
        XNetTable: XNetTable;
        Debug:Debug;
        NPCUtils:NPCUtils;
        FastWarCard:FastWarCard;
        FastWarSpell:FastWarSpell;
        FastWarPlayerHeroSpell:FastWarPlayerHeroSpell;
        KVUtils:KVUtils;
        SoundUtils:SoundUtils;
        EntityUtils:EntityUtils;
        PlayerData:PlayerData;
        FastWarPhaseController:FastWarGamePhaseController;
        FastWarManaUpSpeed:number,
        timeScaleChange:boolean,
        /**
         * 用于人机局势分析，当前默认人机仅用于1v1
         */
        BotGameSituationInfo:{
            towerHealth:number[][]//顺序为good,bad，塔123
            halfUnitNum:number[][][],//含义为：good或bad半场，good或bad单位，左侧或右侧
            halfUnitCost:number[][][],
            halfUnitThreatLevel:number[][][],
            halfUnitHealth:number[][][],
        }
        preCacheNum:number,
        preCacheInfos:string[],
        preCacheAbs:CDOTABaseAbility[],
        treeIndex:number,
    }
    interface CBaseEntity {
         /**
         * 要注意这些参数只有hero有！
         */
        AddPhysicsAcceleration:(v:Vector)=>void;
        AddPhysicsVelocity:(v:Vector)=>void;
        AddStaticVelocity:(name:string,v:Vector)=>void;
        SetStaticVelocity:(name:string,v:Vector)=>void;
        SetNavCollisionType:(n:number)=>void;
        SetPhysicsAcceleration:(v:Vector)=>void;
    }

    interface CDOTABaseAbility {
        Precache(context: CScriptPrecacheContext): void;
    }

    interface CDOTA_BaseNPC {
        fastWarAlive:boolean;
        fastWarUnitType:GoFastWarAIUnitTypeEnum;
        towerIndex:number;
        towerPlayerId:PlayerID;
        fastWarIsGood:boolean;
        cardId:number;
        cardUsesNum:number;
    }
}

/**
 * 这个方法会在game_mode实体生成之后调用，且仅调用一次
 * 因此在这里作为单例模式使用
 **/
export function ActivateModules() {
    if (GameRules.XNetTable == null) {
        // 初始化所有的GameRules模块
        GameRules.XNetTable = new XNetTable();
        // 如果某个模块不需要在其他地方使用，那么直接在这里使用即可
        new GameConfig();
        new GameMode();
        // 初始化测试模块xD
        GameRules.Debug = new Debug()
    }
}
