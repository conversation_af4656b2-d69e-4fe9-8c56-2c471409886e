import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_forest_troll_high_priest_heal extends BaseAbility {
    

    OnAbilityPhaseStart(): boolean {
        this.GetCaster().StartGestureWithFade(GameActivity_t.ACT_DOTA_CAST_ABILITY_1, 0,0.1)
        return true
    }
    
    OnSpellStart(): void {
        let unit = this.GetCaster()
        unit.EmitSound("Fw.Unit.Spell.priest_heal.cast")
        let h = this.GetSpecialValueFor("health")
        this.GetCursorTarget().Heal(h, this)
    }


    Precache(context: CScriptPrecacheContext): void {
       PrecacheResource("particle","particles/spell/luna_lunar_grace_aura/aura_ground.vpcf",context)


    }
}

