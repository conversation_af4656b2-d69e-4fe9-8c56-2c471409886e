import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_huskar_berserkers_blood extends BaseModifier {
    
    par:ParticleID;
    maximum_attack_speed:number;
    maximum_health_regen:number;
    hp_threshold_max:number;
    maximum_magic_resist:number;
    unit:CDOTA_BaseNPC;
    OnCreated(params: object): void {
        this.unit = this.GetParent()
        let ab = this.GetAbility()
        if (IsServer()) {
            this.par = ParticleManager.CreateParticle("particles/units/heroes/hero_huskar/huskar_berserkers_blood.vpcf",ParticleAttachment.ABSORIGIN_FOLLOW, this.unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, this.unit, ParticleAttachment.ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControl(this.par, 1, Vector(0,0,0))
        }
        this.maximum_attack_speed = ab.GetSpecialValueFor("maximum_attack_speed")
        this.maximum_health_regen = ab.GetSpecialValueFor("maximum_health_regen")
        this.hp_threshold_max = ab.GetSpecialValueFor("hp_threshold_max") / 100
        this.maximum_magic_resist = ab.GetSpecialValueFor("maximum_magic_resist")
    }

    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false)
        }
    }

    IsHidden() {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetTexture(): string {
        return "huskar_berserkers_blood"
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/status_effect_bloodrage.vpcf"
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        if (IsServer()) {
            if (this.GetParent().PassivesDisabled()) {
                ParticleManager.SetParticleControl(this.par, 1, Vector(0,0,0))
            } else {
                ParticleManager.SetParticleControl(this.par, 1, Vector(MathUtils.numInRange(this.unit.GetHealth(), this.unit.GetMaxHealth()*this.hp_threshold_max,this.unit.GetMaxHealth(),100,0),0,0))
            }
        }
        if (this.GetParent().PassivesDisabled()) {
            return 0
        } else {
            return MathUtils.numInRange(this.unit.GetHealth(), this.unit.GetMaxHealth()*this.hp_threshold_max,this.unit.GetMaxHealth(),this.maximum_attack_speed,0)
        }
    }

    GetModifierMagicalResistanceBonus(event: ModifierAttackEvent): number {
        if (this.GetParent().PassivesDisabled()) {
            return 0
        } else {
            return MathUtils.numInRange(this.unit.GetHealth(), this.unit.GetMaxHealth()*this.hp_threshold_max,this.unit.GetMaxHealth(),this.maximum_magic_resist,0)
        }
    }

    GetModifierConstantHealthRegen(): number {
        if (this.GetParent().PassivesDisabled()) {
            return 0
        } else {
            return MathUtils.numInRange(this.unit.GetHealth(), this.unit.GetMaxHealth()*this.hp_threshold_max,this.unit.GetMaxHealth(),this.maximum_health_regen,0)
        }
    }


    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            ModifierFunction.MAGICAL_RESISTANCE_BONUS,
            ModifierFunction.HEALTH_REGEN_CONSTANT,
            // ModifierFunction.MOVESPEED_BONUS_CONSTANT,
        ];
    }
    
}
