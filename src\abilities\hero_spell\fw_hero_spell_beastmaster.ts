import { modifier_fw_hero_spell_beastmaster } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_beastmaster";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_hero_spell_beastmaster extends BaseAbility {


     spell:SpellInfoKV;
        GetAOERadius(): number {
            return this.spell.AOERadius
        }
    
    
        OnSpellStart(): void {
            let hero = this.GetCaster()
            let tarPos = this.GetCursorPosition()
            let radius = this.GetAOERadius()
            let par = ParticleManager.CreateParticle("particles/units/heroes/hero_beastmaster/beastmaster_drums_of_slom_stop.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
            ParticleManager.SetParticleControl(par, 0, tarPos)
            ParticleManager.SetParticleControl(par, 1, tarPos)
            ParticleManager.SetParticleControl(par, 2, Vector(radius,0,0))
    
            StartSoundEventFromPositionReliable("Fw.Hero.Spell.beastmaster.cast", tarPos)
            let team = hero.GetTeam()
            let tarTeam = this.GetAbilityTargetTeam()
            let tarFlag = this.GetAbilityTargetFlags()
            let fwTargetType = this.spell.FWTargetType
            let tars = FindUnitsInRadius(
                team,
                tarPos,
                undefined,
                radius,
                tarTeam,
                1 + 2 + 4,
                tarFlag,
                0,
                false,
            )
            if (tars.length > 0) {
                let duration = this.GetSpecialValueFor("duration")
                for (const unit of tars) {
                    if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                        unit.AddNewModifier(hero, this, modifier_fw_hero_spell_beastmaster.name, {duration:duration})
                    }
                }
            }
            
        }
    
    
        Spawn(): void {
            this.spell = GameRules.KVUtils.getSpellInfo("fw_hero_spell_beastmaster")
    
        }

    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/units/heroes/hero_beastmaster/beastmaster_drums_of_slom_stop.vpcf",context)
        PrecacheResource("particle","particles/spell/fw_hero_spell_beastmaster/beastmaster_innerbeast_berserk_passive.vpcf",context)
        PrecacheResource("particle","particles/spell/fw_hero_spell_beastmaster/beastmaster_innerbeast_berserk.vpcf",context)

    }
}

