import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { MathUtils } from "../../../utils/math_utils";


@registerModifier()
export class modifier_fw_hero_spell_beastmaster extends BaseModifier {

    par:ParticleID;
    ues_attackspeed:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        if (IsServer()) {
            let unit = this.GetParent()
            print("创建buff：modifier_fw_hero_spell_beastmaster")
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_beastmaster/beastmaster_innerbeast_berserk.vpcf",ParticleAttachment.ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment.ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
        this.ues_attackspeed = ab.GetSpecialValueFor("ues_attackspeed")
    } 
    OnDestroy(): void {
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par,true)
        }
    }

    GetTexture(): string {
        return "beastmaster_drums_of_slom"
    }

    IsHidden(): boolean {
        return false
    }
    
    IsDebuff(): boolean {
        return false
    }

    IsPurgable(): boolean {
        return true;
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return this.ues_attackspeed
    }

    DeclareFunctions(): ModifierFunction[] {
        return [
            ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
        ];
    }
    
}
