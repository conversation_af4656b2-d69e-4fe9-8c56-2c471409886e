import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_unit_agi_hero extends BaseModifier {
    
    IsHidden() {
        return false;
    }
    
    cooldown:number;
    max_cd:number;
    cost:number;
    max_cost:number;
    OnCreated(params: object): void {
        this.cooldown = this.GetAbility().GetSpecialValueFor("cooldown")
        this.max_cd = this.GetAbility().GetSpecialValueFor("max_cd")
        this.cost = this.GetAbility().GetSpecialValueFor("cost")
        this.max_cost = this.GetAbility().GetSpecialValueFor("max_cost")
        if (IsServer()) {
            Timers.CreateTimer(0.3,()=>{
                this.SetStackCount(this.GetParent().cardUsesNum)
            })
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return false;
    }

    RemoveOnDeath(): boolean {
        return true
    }
    GetModifierPercentageCooldown(): number {
        return Math.min(this.GetStackCount() * this.cooldown,this.max_cost)
    }

    GetModifierPercentageManacostStacking(): number {
        return Math.min(this.GetStackCount() * this.cost,this.max_cd)
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.COOLDOWN_PERCENTAGE,
            ModifierFunction.MANACOST_PERCENTAGE_STACKING,
        ];
    }
    
}
