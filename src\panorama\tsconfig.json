{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES2017", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2017", "DOM"], "outDir": "../../content/panorama/layout/custom_game/hud", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "**/*.d.ts"], "types": ["@moddota/panorama-types"]}