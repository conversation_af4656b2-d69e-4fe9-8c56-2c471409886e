import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_luna_eclipse extends BaseModifier {
    
    damage:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        this.damage = ab.GetSpecialValueFor("extra_attack")
    } 

    OnDestroy(){
        
    }

    IsHidden() {
        return false;
    }

    GetTexture () {
        return "luna_eclipse"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [ModifierState.INVULNERABLE]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    // GetModifierAttackSpeedBonus_Constant(): number {
    //     return -this.attackspeed_slow
    // }
    
    // GetModifierMoveSpeedBonus_Constant(): number {
    //     return -this.movespeed_slow
    // }

    GetModifierPreAttack_BonusDamage(): number {
        return this.damage
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            // ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            ModifierFunction.PREATTACK_BONUS_DAMAGE,
            ];
    }
    
}
