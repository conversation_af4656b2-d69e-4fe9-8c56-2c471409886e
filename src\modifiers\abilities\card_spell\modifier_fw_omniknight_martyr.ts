import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_omniknight_martyr extends BaseModifier {
    
    par:ParticleID;
    magic_resistance:number;
    OnCreated(keys:any): void {
        this.magic_resistance = this.GetAbility().GetSpecialValueFor("magic_resistance")
        if (IsServer()) {
            let unit = this.GetParent()
            unit.Purge(false,true,false,true,true)
            this.par = ParticleManager.CreateParticle("particles/spell/omniknight_martyr/buff.vpcf", ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControlEnt(this.par, 0, unit, ParticleAttachment_t.PATTACH_ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false)
        }
    }

    IsHidden() {
        return false;
    }

    IsDebuff(): boolean {
        return false;
    }

    IsPurgable(): boolean {
        return false;
    }

    IsPurgeException(): boolean {
        return true
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [12]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierMagicalResistanceBonus(event: ModifierAttackEvent): number {
        return this.magic_resistance
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            8,
            ];
    }
    
}
