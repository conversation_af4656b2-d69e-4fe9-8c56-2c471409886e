import { MathUtils } from "./math_utils"


export class KVUtils {

    private GoCardInfos = LoadKeyValues("scripts/npc/go_card.txt")
    private GoSpecialUnitInfos = LoadKeyValues("scripts/npc/go_special_unit.txt")
    private GoUnitTemplateInfos = LoadKeyValues("scripts/npc/go_hero_template.txt")
    private GoFixDeckCards = LoadKeyValues("scripts/npc/go_deck_cards.txt")
    private GoSpells = LoadKeyValues("scripts/npc/go_spell.txt")
    private GoBotStrategy = LoadKeyValues("scripts/npc/go_bot_strategy.txt")
    private GoAbAI = LoadKeyValues("scripts/npc/go_ability_ai.txt")
    private GoPlayerSpell = LoadKeyValues("scripts/npc/go_player_spell.txt")

    private CardsInfo:{[key:number]:CardInfoKV} = {}
    private SpecialUnitInfos:{[key:string]:SpecialUnitInfoKV} = {}
    private UnitTemplateInfos:{[key:string]:UnitTemplateInfoKV} = {}
    private FixDeckCards:{[key:string]:{
        deckCards:number[],
        reserveCards:number[],
        dPlayerHero:string,
        rPlayerHero:string[],
    }} = {}
    private Spells:{[key:string]:SpellInfoKV} = {}
    private GoBotStrategyInfos:{[key:number]:CardComboStrategy} = {}//策略源数据
    private GoBotStrategyForRequire:{[key:number]:number[]} = {}//用于卡组策略引入
    private GoAbAIInfos:{[key:string]:UnitAIInfoKV} = {}
    private GoPlayerSpellInfos:{[key:string]:PlayerHeroForKV} = {}

    constructor () {
        Object.keys(this.GoAbAI).forEach((s)=>{
            let abAI = this.GoAbAI[s]
            let modifiers = []
            if (abAI["AfterFilter"]["ModifierFilter"] != undefined && abAI["AfterFilter"]["ModifierFilter"] != "") {
                modifiers = (abAI["AfterFilter"]["ModifierFilter"] as string).split(",")
            }
            this.GoAbAIInfos[s] = {
                Name:s,
                SelfFilterType:(abAI["SelfFilterType"] as string).startsWith("1"),
                TarFilterType:(abAI["TarFilterType"] as string).startsWith("1"),
                CastType:parseInt((abAI["CastType"] as string).split("-")[0]),
                FindTarType:parseInt((abAI["FindTarType"] as string).split("-")[0]),
                FindRadiusP:{},
                TarFilter:{},	
                SelfFilter:{},
                AfterFilter:{
                    RePos:(abAI["AfterFilter"]["RePos"] as string).startsWith("1"),
                    HealthDeal:abAI["AfterFilter"]["HealthDeal"],
                    Num:abAI["AfterFilter"]["Num"],
                    OnlySelf:(abAI["AfterFilter"]["OnlySelf"] as string).startsWith("1"),
                    Delay:abAI["AfterFilter"]["Delay"],
                    ModifierFilter:modifiers,
                },
            }
            if (this.GoAbAIInfos[s].FindTarType == 1) {
                this.GoAbAIInfos[s].FindRadiusP = {
                    Radius:abAI["FindRadiusP"]["Radius"],
                    TargetTeam:parseInt((abAI["FindRadiusP"]["TargetTeam"] as string).split("-")[0]),
                    UnitTargetType:parseInt((abAI["FindRadiusP"]["UnitTargetType"] as string).split("-")[0]),
                    UnitTargetFlags:parseInt((abAI["FindRadiusP"]["UnitTargetFlags"] as string).split("-")[0]),
                    FindOrder:parseInt((abAI["FindRadiusP"]["FindOrder"] as string).split("-")[0]),
                }
            }
            if (this.GoAbAIInfos[s].SelfFilterType) {
                this.GoAbAIInfos[s].SelfFilter = {
                    PosY:abAI["SelfFilter"]["PosY"],
                    Health:abAI["SelfFilter"]["Health"],
                }
            }
            if (this.GoAbAIInfos[s].TarFilterType) {
                this.GoAbAIInfos[s].TarFilter = {
                    Health:abAI["TarFilter"]["Health"],
                    FindSelf:parseInt((abAI["TarFilter"]["FindSelf"] as string).split("-")[0]),
                }
            }	
        })
        // print("初始化技能自动释放配置！")
        // DeepPrintTable(this.GoAbAIInfos)
        Object.keys(this.GoCardInfos).forEach((s)=>{
            let cardInfo = this.GoCardInfos[s]
            // print("格式化："+s)
            let tarsAttack = [1,2,3,4,5,6,7]
            let tarsDefense = [8,9,10,11,12]
            if (cardInfo["BotCardAI"]["Tar"] != undefined && cardInfo["BotCardAI"]["Tar"] != "") {
                if ((cardInfo["BotCardAI"]["Tar"] as string).indexOf(";") == -1) {
                    tarsAttack = (cardInfo["BotCardAI"]["Tar"] as string).split(",").map((s)=>parseInt(s))
                    tarsDefense = [...tarsAttack]
                } else {
                    let arrsys = (cardInfo["BotCardAI"]["Tar"] as string).split(";")
                    tarsAttack = arrsys[0].split(",").map((s)=>parseInt(s))
                    tarsDefense = arrsys[1].split(",").map((s)=>parseInt(s))
                }
            }
            let unitInfo:CardUnitTInfo = undefined
            let spellInfo:CardSpellInfo = undefined 
            if (cardInfo["CardType"] == "unit") {
                let ts = []
                for (let i = 1; i < 5; i++) {
                    if (cardInfo["UnitCardInfo"]["Template"+i] != undefined 
                        && cardInfo["UnitCardInfo"]["Num"+i] != undefined) {
                        ts.push({
                            Template:cardInfo["UnitCardInfo"]["Template"+i],
                            Num:cardInfo["UnitCardInfo"]["Num"+i],
                        })                            
                    }
                }
                let g = (cardInfo["UnitCardInfo"]["LayGroupNum"] as string).split(",").map((v)=>parseInt(v))
                unitInfo = {
                    LayType:cardInfo["UnitCardInfo"]["LayType"],
                    Delay:cardInfo["UnitCardInfo"]["Delay"],
                    LayGroupNum:g,
                    Template:ts,
                }
            } else if (cardInfo["CardType"] == "spell") {
                spellInfo = {
                    SpellTemplate:cardInfo["SpellCardInfo"]["SpellTemplate"],
                    UICardBack:cardInfo["SpellCardInfo"]["UICardBack"],
                }
            }
            let enable = true
            if (cardInfo["Enable"] == 0) {
                enable = false
            }
            let EnableForDeckCards = true
            if (cardInfo["EnableForDeckCards"] == 0) {
                EnableForDeckCards = false
            }
            let ExtraData = {
                HandCardCooldown:0,
                HandCardCooldown_UseAdd:0,
                HandCardCost_UsesCheckNum:0,
                HandCardCost:0,
                HandCardCost_MaxNum:0,
            }
            if (cardInfo["ExtraData"] != undefined) {
                if (cardInfo["ExtraData"]["HandCardCooldown"] != undefined && cardInfo["ExtraData"]["HandCardCooldown"] != "") {
                    let s = (cardInfo["ExtraData"]["HandCardCooldown"] as string).split("|")
                    ExtraData.HandCardCooldown = parseInt(s[0])
                    ExtraData.HandCardCooldown_UseAdd = parseInt(s[1])
                }
                if (cardInfo["ExtraData"]["HandCardCost"] != undefined && cardInfo["ExtraData"]["HandCardCost"] != "") {
                    let s = (cardInfo["ExtraData"]["HandCardCost"] as string).split("|")
                    ExtraData.HandCardCost_UsesCheckNum = parseInt(s[0])
                    ExtraData.HandCardCost = parseInt(s[1])
                    ExtraData.HandCardCost_MaxNum = parseInt(s[2])
                }
            }
            this.CardsInfo[parseInt(s.toString())] = {
                Index:parseInt(s.toString()),
                name:cardInfo["Name"],
                Enable:enable,
                EnableForDeckCards:EnableForDeckCards,
                chName:cardInfo["ChName"],
                cardType:cardInfo["CardType"],
                CardUIType:cardInfo["CardUIType"],
                layCost:cardInfo["LayCost"],
                UnitCardInfo:unitInfo,
                SpellCardInfo:spellInfo,
                BotCardAI:{
                    CardTag:parseInt((cardInfo["BotCardAI"]["CardTag"] as string).split("-")[0]),
                    TarAttack:tarsAttack,
                    TarDefense:tarsDefense,
                },
                ExtraData:ExtraData,
            }
        })
        // print("载入技能自动释放配置！")
        // DeepPrintTable(this.CardsInfo)
        Object.keys(this.GoUnitTemplateInfos).forEach((s)=>{
            let unitT = this.GoUnitTemplateInfos[s]
            // print("格式化："+s)
            let abs = []
            for (let i = 1; i < 19 ; i++) {
                if (unitT["Ability"+i] != undefined && unitT["Ability"+i] != "") {
                    abs.push(unitT["Ability"+i])
                }
            }
            let unitAIs:{
                [keys:number]:{
                    autoNum:number,
                    spellAI:UnitAIInfoKV[],
                },
            } = undefined
            let uiHandSpell = ""
            if (unitT["UnitAI"] != undefined && unitT["UnitAI"]["AutoNum"] != undefined) {
                unitAIs = {}
                let n = [-1,-1,-1]
                if (unitT["UnitAI"]["AutoNum"] != undefined && unitT["UnitAI"]["AutoNum"] != "") {
                    n = (unitT["UnitAI"]["AutoNum"] as string).split(",").map((v)=>parseInt(v))
                }
                Object.keys(unitT["UnitAI"]["SpellAI"]).forEach((v,index)=>{
                    let unitAI:UnitAIInfoKV[] = []
                    let s = unitT["UnitAI"]["SpellAI"][v.toString()] as string
                    if (s != undefined && s != "") {
                        if (s == "-1" ) {
                            uiHandSpell = abs[parseInt(v.toString())-1]
                        } else {
                            let spellAIs = [s]
                            if (s.indexOf(",") > 0) {
                                spellAIs = s.split(",")
                            }
                            for (const spellAI of spellAIs) {
                                let abAI = this.GoAbAIInfos[spellAI]
                                if (abAI != undefined) {
                                    unitAI.push(abAI)
                                }
                            }
                            unitAIs[parseInt(v.toString())] = {
                                autoNum:n[parseInt(v.toString())-1],
                                spellAI:unitAI,
                            }
                        }
                    }
                })
            }
            let modifiers = []
            if (unitT["SpawnActivityModifiers"] != undefined && unitT["SpawnActivityModifiers"] != "") {
                modifiers = (unitT["SpawnActivityModifiers"] as string).split(",")
            }
            this.UnitTemplateInfos[s] = {
                name:s,
                unitType:this.strToUnitType(unitT["UnitType"]),
                AttackType:unitT["AttackType"],
                modelScale:unitT["ModelScale"],
                isHealthBarPips:unitT["HealthBarPips"]==1,
                attackHealthPips:parseInt(unitT["AttackHealthPips"]),
                statusHealth:parseInt(unitT["StatusHealth"]),
                unitAI:unitAIs,
                UIHandSpell:uiHandSpell,
                BaseAttackSpeed:parseInt(unitT["BaseAttackSpeed"])/100,
                unitDuration:parseInt(unitT["UnitDuration"]),
                ParEntUnit:unitT["ParEntUnit"],
                Abilities:abs,
                RingRadius:unitT["RingRadius"],
                SpawnActivityModifiers:modifiers,
            }
        })
        // DeepPrintTable(this.UnitTemplateInfos)
        Object.keys(this.GoFixDeckCards).forEach((s)=>{
            let deckCards = this.GoFixDeckCards[s]
            // print("格式化："+s)
            let dCards = []
            if (deckCards["Cards"] != undefined && deckCards["Cards"] != "") {
                dCards = (deckCards["Cards"] as string).split(",").map((s)=>parseInt(s))
            }
            let rCards= []
            if (deckCards["ReserveCards"] != undefined && deckCards["ReserveCards"] != "") {
                rCards = (deckCards["ReserveCards"] as string).split(",").map((s)=>parseInt(s))
            }
            let rHeros:string[]  = []
            if (deckCards["ReservePlayerHero"] != undefined && deckCards["ReservePlayerHero"] != "") {
                rHeros = (deckCards["ReservePlayerHero"] as string).split(",")
            }
            let dHeros = ""
            if (deckCards["PlayerHero"] != undefined && deckCards["PlayerHero"] != "") {
                dHeros = deckCards["PlayerHero"]
            } else if (rHeros.length > 0) {
                dHeros = MathUtils.getRandomElements(rHeros,1)[0]
            }
            let r1 = new Set([...dCards])
            let r2 = new Set([...dCards,...rCards])
            r1.forEach((v)=>{
                r2.delete(v)
            })
            let r3 = new Set([...rHeros])
            r3.delete(dHeros)
            this.FixDeckCards[parseInt(s.toString())] = {
                deckCards:[...r1],
                reserveCards:[...r2],
                dPlayerHero:dHeros,
                rPlayerHero:[...r3],
            }
        })
        // DeepPrintTable(this.FixDeckCards)
        Object.keys(this.GoSpells).forEach((s)=>{
            let spell = this.GoSpells[s]
            let unitT:string[] = []
            if (spell["UnitTemplate"] != undefined && spell["UnitTemplate"] != "") {
                unitT = (spell["UnitTemplate"]as string).split(",")
            }
            // DeepPrintTable(spell["Precache"])
            let spellInfoKV:SpellInfoKV = {
                abName:s,
                EffectPar:spell["EffectPar"],
                UnitTemplate:unitT,
                AOERadius:spell["AOERadius"],
                FWTargetType:this.strToUnitType(spell["FWTargetType"]),
                Precache:spell["Precache"],
                CoolDown:spell["AbilityCooldown"],
            }
            this.Spells[s] = spellInfoKV
        })
        Object.keys(this.GoBotStrategy).forEach((s)=>{
            let botStr = this.GoBotStrategy[s]
            // print("格式化："+s)
            if (botStr["RelatedCards"] == undefined) {
                return
            }
            let cards = (botStr["RelatedCards"] as string).split(",").map((s)=>parseInt(s))
            let minCost = 0;
            cards.forEach((i)=>{
                minCost += this.CardsInfo[i].layCost
            })
            let tarsAttack = [1,2,3,4,5,6,7]
            let tarsDefense = [8,9,10,11,12]
            if (botStr["Tar"] != undefined && botStr["Tar"] != "") {
                if ((botStr["Tar"] as string).indexOf(";") == -1) {
                    tarsAttack = (botStr["Tar"] as string).split(",").map((s)=>parseInt(s))
                    tarsDefense = [...tarsAttack]
                } else {
                    let arrsys = (botStr["Tar"] as string).split(";")
                    tarsAttack = arrsys[0].split(",").map((s)=>parseInt(s))
                    tarsDefense = arrsys[1].split(",").map((s)=>parseInt(s))
                }
            }
            this.GoBotStrategyInfos[parseInt(s.toString())] = {
                Index:parseInt(s),
                RelatedCards:cards,
                Priority:botStr["Priority"],
                AttackLevel:botStr["AttackLevel"],
                TarAttack:tarsAttack,
                TarDefense:tarsDefense,
                minCost:minCost,
            }
            for (const cardIndex of cards) {
                let info = this.GoBotStrategyForRequire[cardIndex]
                if (info == undefined) {
                    info = []
                }
                info.push(parseInt(s))
                this.GoBotStrategyForRequire[cardIndex] = info
            }
        })
        Object.keys(this.GoSpecialUnitInfos).forEach((s)=>{
            let special = this.GoSpecialUnitInfos[s]
            let e = special["Entrance"]
            if (e == undefined || e == "") {
                e = "default"
            }
            this.SpecialUnitInfos[s] = {
                name:s,
                EntranceSound:special["EntranceSound"],
                DeathSound:special["DeathSound"],
                ExtraModel:special["ExtraModel"],
                Entrance:e,
            }
        })
        Object.keys(this.GoPlayerSpell).forEach((s)=>{
            let hero = this.GoPlayerSpell[s]
            let CardIndex = []
            if (hero["CardIndex"] != undefined && hero["CardIndex"] != "") {
                // print(hero["CardIndex"])
                CardIndex = (hero["CardIndex"] as String).toString().split(",").map((s)=>parseInt(s))
            }

            this.GoPlayerSpellInfos[s] = {
                HeroName:s,
                HeroImage:hero["HeroImage"],
                SpellName:hero["SpellName"],
                CardIndex:CardIndex,
            }
        })
        // print("GoBotStrategyInfos")
        // DeepPrintTable(this.GoBotStrategyInfos)
        // print("GoBotStrategyForRequire")
        // DeepPrintTable(this.GoBotStrategyForRequire)
    }

    private strToUnitType (str:string) {
        let flag:GoFastWarAIUnitTypeEnum;
        switch (str) {
            case "all":
                flag = GoFastWarAIUnitTypeEnum.ALL
                break;
            case "ground":
                flag = GoFastWarAIUnitTypeEnum.GROUND
                break;
            case "building":
                flag = GoFastWarAIUnitTypeEnum.BUILDING
                break;
            default:
                flag = GoFastWarAIUnitTypeEnum.GROUND
                break;
        }
        return flag
    }

    public getSpellInfo (spellName:string) {
        return this.Spells[spellName]
    }

    public getCardsInfo (cardIndex:number) {
        return this.CardsInfo[cardIndex]
    }

    public getBotCardsStr (cardIndexs:number[]){
        let res:number[] = []
        let strRes:CardComboStrategy[] = []
        let cardRes:{
            [keys:number]:number
        } = {}
        for (const cardIndex of cardIndexs) {
            let requireCards = this.GoBotStrategyForRequire[cardIndex]
            if (requireCards != undefined && requireCards.length > 0) {
                requireCards.forEach((s)=>{
                    let strInfo = this.GoBotStrategyInfos[s]
                    // DeepPrintTable(strInfo)
                    if (res.indexOf(s) == -1) {
                        res.push(s)
                        strRes.push(strInfo)
                    }
                    if (cardRes[cardIndex] == undefined) {
                        cardRes[cardIndex] = 0
                    }
                    cardRes[cardIndex] += strInfo.Priority
                })
            }
        }
        return  {
            strRes:strRes,
            cardRes:cardRes,
        }
    }


    public getFixDeckCards (playerId:number, deckOrReserve:boolean) {
        if (this.FixDeckCards[playerId] != undefined   ) {
            return deckOrReserve?this.FixDeckCards[playerId].deckCards:this.FixDeckCards[playerId].reserveCards
        }
        return [1,2,3,4,5,6,7,8]
    }
    
    public getFixDPlayerHero (playerId:number) {
        if (this.FixDeckCards[playerId] != undefined   ) {
            return this.FixDeckCards[playerId].dPlayerHero
        }
        return "fw_hero_sven"
    }
    public getFixRPlayerHero (playerId:number) {
        if (this.FixDeckCards[playerId] != undefined   ) {
            return this.FixDeckCards[playerId].rPlayerHero
        }
        return []
    }

    public getSpecialUnitInfoUntreated (unitName:string) {
        return this.GoSpecialUnitInfos[unitName]
    }

    public getSpecialUnitInfoTreated (unitName:string) {
        return this.SpecialUnitInfos[unitName]
    }

    /**
     * 获取未处理的单位模板信息
     */
    public getUnitTemplateInfoUntreated (template:string) {
        return this.GoUnitTemplateInfos[template]
    }

    /**
     * 获取处理过的单位模板信息
     */
    public getUnitTemplateInfoTreated (template:string) {
        return this.UnitTemplateInfos[template]
    }

    public getPlayerHeroInfo (heroName:string) {
        return this.GoPlayerSpellInfos[heroName]
    }

}