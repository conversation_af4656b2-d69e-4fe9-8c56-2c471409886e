import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";
import { modifier_fw_zombie_extra_debuff } from "./modifier_fw_zombie_extra_debuff";


@registerModifier()
export class modifier_fw_summon_zombie extends BaseModifier {
    
    timer:string;
    OnCreated(keys:any): void {
        if (IsServer()) {
            let unit = this.GetCaster()
            let playerID = unit.GetPlayerOwnerID()
            let team = unit.GetTeam()
            let pos = unit.GetAbsOrigin()
            let ab = this.GetAbility()
            let summon_interval = ab.GetSpecialValueFor("summon_interval")
            let num = ab.GetSpecialValueFor("num")
            let extra_num = ab.GetSpecialValueFor("extra_num")
            let creepTempName = "creep_default_undying_zombie"
            let tempInfo = GameRules.KVUtils.getUnitTemplateInfoTreated(creepTempName)
            let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, creepTempName, team)
            // print("创建modifier_fw_summon_zombie")
            this.SetStackCount(0)

            this.timer = GameRules.FastWarSpell.startIntervalSpell(0,99,summon_interval,
            ()=>{
                GameRules.NPCUtils.createUnitForSpell(playerID,unit,team,unit.cardId,1,tempInfo,num,unitName,pos,0, unit.cardUsesNum)
            },()=>{
                GameRules.NPCUtils.createUnitForSpell(playerID,unit,team,unit.cardId,1,tempInfo,num,unitName,pos,0, unit.cardUsesNum)
            },()=>{
                
            })
        }
    } 
    
    
    OnDeath(event: ModifierInstanceEvent): void {
        if (IsServer()) {
            if (event.unit.HasModifier(modifier_fw_zombie_extra_debuff.name)) {
                let modifiers = event.unit.FindAllModifiersByName(modifier_fw_zombie_extra_debuff.name)
                let owner = modifiers[0].GetCaster().GetOwner() as CDOTA_BaseNPC
                // print("监听到带debuff的任意单位死亡,来源："+owner.GetUnitName() + ","+owner.entindex()+"，当前墓碑单位为："+this.GetParent().entindex())
                if (owner.entindex() == this.GetParent().entindex()) {
                    this.SetStackCount(this.GetStackCount() + 1)
                }
            }
            if (event.unit.entindex() == this.GetParent().entindex()) {
                // print("监听到墓碑死亡")
                Timers.RemoveTimer(this.timer)
                let ab = this.GetAbility()
                let last_num = ab.GetSpecialValueFor("last_num")
                let unit = this.GetCaster()
                let playerID = unit.GetPlayerOwnerID()
                let team = unit.GetTeam()
                let pos = unit.GetAbsOrigin()
                let creepTempName = "creep_default_undying_zombie"
                let tempInfo = GameRules.KVUtils.getUnitTemplateInfoTreated(creepTempName)
                let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, creepTempName, team)
                GameRules.NPCUtils.createUnitForSpell(playerID,unit,team,unit.cardId,1,tempInfo,last_num,unitName,pos,0, unit.cardUsesNum)
            }
        }
    }

    OnDestroy(){
        if (IsServer()) {
            
        }
    }

    IsHidden() {
        return false;
    }
    
    CheckState() {
        return {
            // [ModifierState.INVULNERABLE]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.ON_DEATH,
            // ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ];
    }
    
}
