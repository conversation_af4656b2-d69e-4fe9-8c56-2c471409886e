import { modifier_fw_summon_zombie } from "../../modifiers/abilities/unit_spell/modifier_fw_summon_zombie";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";
import { MathUtils } from "../../utils/math_utils";

@registerAbility()
export class fw_summon_zombie extends BaseAbility {
    
    spell:SpellInfoKV;
    OnSpellStart(): void {
        let hero = this.GetCaster()
        hero.AddNewModifier(hero, this, modifier_fw_summon_zombie.name, {})
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_summon_zombie")
        if (this.spell.UnitTemplate.length > 0 ) {
            if (IsServer()) {
                let unit = this.GetCaster()
                let team = unit.GetTeam()
                let playerID = unit.GetPlayerOwnerID()
                for (const temp of this.spell.UnitTemplate) {
                    let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(playerID, temp, team)
                    GameRules.preCacheInfos.push(unitName)
                }
                // print("初始化墓碑附属代为，来自玩家："+playerID)
                // DeepPrintTable(GameRules.preCacheInfos)
                PrecacheItemByNameAsync("item_for_precache", ()=>{
                    // print("预载单位完成")
                })
            }
        }
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","",context)
    }
}

