import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_invis extends BaseModifier {
    
    extra_movespeed:number;
    OnCreated(keys:any): void {
        this.extra_movespeed = this.GetAbility().GetSpecialValueFor("extra_movespeed")
        this.StartIntervalThink(0.3)
    } 

    OnIntervalThink(): void {
        if (this.GetParent().HasModifier("modifier_truesight")) {
            this.Destroy()
        }
    }

    OnDestroy(){
        
    }

    IsHidden() {
        return false;
    }

    IsDebuff(): boolean {
        return false
    }

    IsPurgable(): boolean {
        return true
    }

    GetTexture () {
        return "bounty_hunter_wind_walk"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [ModifierState.DISARMED]: true,
            [ModifierState.ATTACK_IMMUNE]: true,
            [ModifierState.NO_HEALTH_BAR]: true,
        }   
    }

    RemoveOnDeath(): boolean {
        return true
    }

    GetModifierInvisibilityLevel(): number {
        return 1
    }

    GetModifierMoveSpeedBonus_Constant(): number {
        return this.extra_movespeed
    }
  
    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.INVISIBILITY_LEVEL,
            ModifierFunction.MOVESPEED_BONUS_CONSTANT,
        ];
    }
    
}
