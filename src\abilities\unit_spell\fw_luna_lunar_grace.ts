import { modifier_fw_luna_lunar_grace_aura } from "../../modifiers/abilities/unit_spell/modifier_fw_luna_lunar_grace_aura";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_luna_lunar_grace extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_luna_lunar_grace_aura.name
    }
    
    Precache(context: CScriptPrecacheContext): void {
       PrecacheResource("particle","particles/spell/luna_lunar_grace_aura/aura_ground.vpcf",context)
       PrecacheResource("particle","particles/spell/luna_lunar_grace_aura/mirana_solar_blessing_buff_hands_glow.vpcf",context)
    }
}

