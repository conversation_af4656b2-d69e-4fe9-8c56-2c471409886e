import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_hero_spell_sven extends BaseModifier {
    
    par:ParticleID;
    max_barrier:number;
    current_barrier:number;
    OnCreated(keys:any): void {
        let ab = this.GetAbility()
        this.max_barrier = ab.GetSpecialValueFor("shield")
        this.current_barrier = ab.GetSpecialValueFor("shield")
        if (IsServer()) {
            let unit = this.GetParent()
            this.par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_sven/buff/sven_warcry_barrier.vpcf", ParticleAttachment.ABSORIGIN_FOLLOW, unit)
            ParticleManager.SetParticleControl(this.par, 0, unit.GetAbsOrigin())
            let h = unit.GetBaseHealthBarOffset()*0.6
            ParticleManager.SetParticleControl(this.par, 3, Vector(h,1,1))

            this.SetHasCustomTransmitterData(true)
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false);
        }
    }
    
    IsDebuff(): boolean {
        return false;
    }

    IsHidden() {
        return false;
    }

    IsPurgable(): boolean {
        return true;
    }


    GetTexture () {
        return "sven_warcry"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [ModifierState.INVULNERABLE]: true,
        }   
    }

    AddCustomTransmitterData () {
        return {
            max_barrier:this.max_barrier,
            current_barrier:this.current_barrier
        }
    }
    
    HandleCustomTransmitterData (data) {
        this.max_barrier = data.max_barrier
        this.current_barrier = data.current_barrier
    }

    GetModifierIncomingPhysicalDamageConstant(event: ModifierAttackEvent): number {
        if (IsClient()) {
            return event.report_max ? this.max_barrier : this.current_barrier
        } else {
            if (event.damage > this.current_barrier) {
                this.Destroy()
                return -this.current_barrier
            } else {
                this.current_barrier = this.current_barrier - event.damage
                this.SendBuffRefreshToClients()
                return -event.damage
            }
        }
    }

    RemoveOnDeath(): boolean {
        return true
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.INCOMING_PHYSICAL_DAMAGE_CONSTANT,
            // ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ];
    }
    
}
