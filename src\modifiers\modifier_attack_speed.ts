import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";


@registerModifier()
export class modifier_attack_speed extends BaseModifier {
    
    num:number = 0;
    OnCreated(): void {
        let special = GameRules.KVUtils.getSpecialUnitInfoUntreated(this.GetParent().GetUnitName())
        if (special != undefined) {
            this.num = Math.round((GameRules.KVUtils.getUnitTemplateInfoTreated(special["Template"]).BaseAttackSpeed - this.GetParent().GetAttackSpeed()) * 100)
        }
        // print("创建攻速覆盖:"+this.num)
    } 

    OnDestroy(){

    }

    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [6]: true,
        }   
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return this.num
    }

    GetModifierMoveSpeedBonus_Constant(): number {
        return 0
    }
    

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            1,
            2,
        ];
    }

   
    
}
