import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_freezing_field_debuff extends BaseModifier {
    
    movespeed_slow:number;
    attackspeed_slow:number;
    par:ParticleID;
    OnCreated(): void {
        let hero = this.GetParent()
        let ab = this.GetAbility()
        this.movespeed_slow = ab.GetSpecialValueFor("movespeed_slow")
        this.attackspeed_slow = ab.GetSpecialValueFor("attackspeed_slow")
    
        if (IsServer()) {
            this.par = ParticleManager.CreateParticle("particles/spell/crystal_maiden_freezing_field/maiden_frostbite_buff.vpcf", ParticleAttachment.ABSORIGIN_FOLLOW, hero,)
            ParticleManager.SetParticleControlEnt(this.par, 0, hero, ParticleAttachment.ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControl(this.par, 1, hero.GetAbsOrigin())
            
            hero.EmitSound("Fw.Cards.Spell.freezing_field.freeze")
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false);
        }

    }

    IsHidden() {
        return false;
    }

    GetTexture () {
        return "crystal_maiden_freezing_field_alt1"
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/status_effect_frost_lich.vpcf"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            // [ModifierState.INVULNERABLE]: true,
        }   
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return -this.attackspeed_slow
    }
    
    GetModifierMoveSpeedBonus_Constant(): number {
        return -this.movespeed_slow
    }


    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            ModifierFunction.MOVESPEED_BONUS_CONSTANT,
            ];
    }
    
}
