import { modifier_fw_hero_spell_saint } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_saint";
import { modifier_fw_hero_spell_saint_lv2 } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_saint_lv2";
import { modifier_fw_hero_spell_saint_lv3 } from "../../modifiers/abilities/hero_spell/modifier_fw_hero_spell_saint_lv3";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_hero_spell_saint extends BaseAbility{

    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        fun(this, this.spell, modifier_fw_hero_spell_saint.name, false)
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_hero_spell_saint")
    }

    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/spell/fw_hero_spell_saint/silencer_global_silence.vpcf",context)
        PrecacheResource("particle","particles/spell/fw_hero_spell_saint/silencer_curse.vpcf",context)
        PrecacheResource("particle","particles/spell/fw_hero_spell_saint/silencer_curse2.vpcf",context)
    }
}

@registerAbility()
export class fw_hero_spell_saint_lv2 extends BaseAbility{
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        fun(this, this.spell, modifier_fw_hero_spell_saint_lv2.name, false)
    }
    
    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_hero_spell_saint_lv2")
    }


    Precache(context: CScriptPrecacheContext): void {

    }
}
@registerAbility()
export class fw_hero_spell_saint_lv3 extends BaseAbility{
    
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }
   
    OnSpellStart(): void {
        fun(this, this.spell, modifier_fw_hero_spell_saint_lv3.name, true)
    }
    
    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_hero_spell_saint_lv3")
    }

    Precache(context: CScriptPrecacheContext): void {


    }
}

let fun = function (ab:CDOTABaseAbility, spell:SpellInfoKV, mName:string, isLv3:boolean) {
    let hero = ab.GetCaster()
    let tarPos = ab.GetCursorPosition()
    let radius = ab.GetAOERadius()
    let par = ParticleManager.CreateParticle("particles/spell/fw_hero_spell_saint/silencer_global_silence.vpcf", ParticleAttachment_t.PATTACH_WORLDORIGIN, undefined)
    ParticleManager.SetParticleControl(par, 0, tarPos)
    ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))

    StartSoundEventFromPositionReliable("Fw.Hero.Spell.saint.cast", tarPos)
    let team = hero.GetTeam()
    let tarTeam = ab.GetAbilityTargetTeam()
    let tarFlag = ab.GetAbilityTargetFlags()
    let fwTargetType = spell.FWTargetType
    let tars = FindUnitsInRadius(
        team,
        tarPos,
        undefined,
        radius,
        tarTeam,
        1 + 2 + 4,
        tarFlag,
        0,
        false,
    )
    if (tars.length > 0) {
        let duration = ab.GetSpecialValueFor("duration")
        for (const unit of tars) {
            if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                if (isLv3) {
                    if (unit.GetTeam() == team) {
                        unit.Purge(false, true, false, false, false)
                    } else {
                        unit.Purge(true, false, false, false, false)
                        unit.AddNewModifier(hero, ab, mName, {duration:duration})
                    }
                } else {
                    unit.AddNewModifier(hero, ab, mName, {duration:duration})
                }
            }
        }
    }
}
