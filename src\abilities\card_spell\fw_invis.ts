import { modifier_fw_invis_grad } from "../../modifiers/abilities/card_spell/modifier_fw_invis_grad";
import { modifier_fw_time_travel } from "../../modifiers/abilities/card_spell/modifier_fw_time_travel";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_invis extends BaseAbility {
    
    spell:SpellInfoKV;
    GetAOERadius(): number {
        return this.spell.AOERadius
    }

    OnSpellStart(): void {
        let hero = this.GetCaster()
        let ab = this
        let team = hero.GetTeam()
        let tarPos = this.GetCursorPosition()
        let radius = this.GetAOERadius()
        let tarTeam = this.GetAbilityTargetTeam()
        let tarFlag = this.GetAbilityTargetFlags()
        let fwTargetType = this.spell.FWTargetType

        StartSoundEventFromPositionReliable("Fw.Cards.Spell.invis.cast", tarPos)
        
        let par = ParticleManager.CreateParticle("particles/spell/fw_invis/invis_cast.vpcf", ParticleAttachment.WORLDORIGIN, undefined)
        ParticleManager.SetParticleControl(par, 0, tarPos)
        ParticleManager.SetParticleControl(par, 1, Vector(radius,0,0))

        let tars = FindUnitsInRadius(
            team,
            tarPos,
            undefined,
            radius,
            tarTeam,
            UnitTargetType.HERO + UnitTargetType.CREEP + UnitTargetType.BUILDING,
            tarFlag,
            FindOrder.ANY,
            false,
        )
        if (tars.length > 0) {
            let delay = this.GetSpecialValueFor("delay")
            for (const unit of tars) {
                if (unit.fastWarUnitType != undefined && (fwTargetType == GoFastWarAIUnitTypeEnum.ALL || unit.fastWarUnitType == fwTargetType)) {
                    unit.AddNewModifier(hero, ab, modifier_fw_invis_grad.name, {delay:delay,duration:delay+0.5}) 
                }
            }
        }
    }

    Spawn(): void {
        this.spell = GameRules.KVUtils.getSpellInfo("fw_invis")
    }
    
    Precache(context: CScriptPrecacheContext): void {
        PrecacheResource("particle","particles/units/heroes/hero_bounty_hunter/bounty_hunter_windwalk.vpcf",context)
        PrecacheResource("particle","particles/spell/fw_invis/invis_cast.vpcf",context)
    }
}

