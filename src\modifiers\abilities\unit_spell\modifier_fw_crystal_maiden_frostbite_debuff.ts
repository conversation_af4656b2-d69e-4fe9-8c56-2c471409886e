import { BaseModifier, registerModifier } from "../../../utils/dota_ts_adapter";


@registerModifier()
export class modifier_fw_crystal_maiden_frostbite_debuff extends BaseModifier {
    
    par:ParticleID;
    attack_speed_debuff:number;
    OnCreated(): void {
        let hero = this.GetParent()
        this.attack_speed_debuff = this.GetAbility().GetSpecialValueFor("attack_speed_debuff")
        if (IsServer()) {
            hero.EmitSound("hero_Crystal.frostbite")
            this.par = ParticleManager.CreateParticle("particles/spell/crystal_maiden_freezing_field/maiden_frostbite_buff.vpcf", ParticleAttachment.ABSORIGIN_FOLLOW, hero,)
            ParticleManager.SetParticleControlEnt(this.par, 0, hero, ParticleAttachment.ABSORIGIN_FOLLOW, "attach_hitloc", Vector(0,0,0), true)
            ParticleManager.SetParticleControl(this.par, 1, hero.GetAbsOrigin())
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            ParticleManager.DestroyParticle(this.par, false);
        }

    }

    IsHidden() {
        return false;
    }

    IsDebuff(): boolean {
        return true
    }

    IsPurgable(): boolean {
        return true
    }

    GetTexture () {
        return "crystal_maiden_frostbite"
    }

    GetStatusEffectName(): string {
        return "particles/status_fx/status_effect_frost_lich.vpcf"
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    CheckState() {
        return {
            [ModifierState.ROOTED]: true,
        }   
    }

    GetModifierAttackSpeedBonus_Constant(): number {
        return this.attack_speed_debuff
    }

    DeclareFunctions(): ModifierFunction[] {
        // return []
        return [
            ModifierFunction.ATTACKSPEED_BONUS_CONSTANT,
            ];
    }
    
}
