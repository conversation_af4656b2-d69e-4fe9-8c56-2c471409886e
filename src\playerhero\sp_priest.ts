import { modifier_fw_hero_spell_priest } from "../modifiers/abilities/hero_spell/modifier_fw_hero_spell_priest";
import { PlayerHeroSpellHandle } from "./PlayerHeroSpellHandle";


export class PlayerHeroPriest extends PlayerHeroSpellHandle {

    private num = 0
    AfterCardUsing(data:{UsingCardType:string}): void {
        if (data.UsingCardType == "unit" && this.num < 3) {
            this.num += 1
            this.nowSpellInfo.num = this.num + "/3"
        }
    }

    GetCardBuff(data):ModifierData[] { 
        if (this.ab == undefined) {
            this.ab = this.hero.FindAbilityByName(this.spellInfo.SpellName)
        }
        if (this.num >= 3 && this.nowSpellInfo.nextUsingTime <= GameRules.GetGameTime()) {
            this.nowSpellInfo.nextUsingTime = GameRules.GetGameTime() + this.nowSpellInfo.cooldown
            this.num = -1
            this.nowSpellInfo.num = "0/3"
            return [
                {
                    ab:this.ab,
                    caster:this.hero,
                    modifierName:modifier_fw_hero_spell_priest.name,
                    data:{} 
                }
            ]
        }
        return []
    }

    private ab:CDOTABaseAbility;
    constructor (playerId:PlayerID,spellInfo:PlayerHeroForKV
    ) {
        super(playerId,spellInfo)
        
        let sp = GameRules.KVUtils.getSpellInfo(spellInfo.SpellName)
        this.nowSpellInfo = {
            HeroName: spellInfo.HeroName,
            HeroImage: spellInfo.HeroImage,
            SpellCardIndex: -1,
            SpellName: spellInfo.SpellName,
            cost: 0,
            nextUsingTime: GameRules.GetGameTime() + sp.CoolDown,
            cooldown:sp.CoolDown,
            num:"0/3",
            usesNum:0,
        }
    }
}