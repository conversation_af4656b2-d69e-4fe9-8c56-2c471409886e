import { BaseModifier, registerModifier } from "../utils/dota_ts_adapter";



@registerModifier()
export class modifier_unit_sound extends BaseModifier {

    deathSound:string;
    OnCreated(keys:any): void {
        if (IsServer()) {
            let entranceSound = keys.entranceSound as string
            if (entranceSound != undefined && entranceSound != "") {
                let es = entranceSound.split(",")
                for (const s of es) {
                    this.GetParent().EmitSound(s)
                }
            }
            this.deathSound = keys.deathSound as string
        }
    } 

    OnDestroy(){
        if (IsServer()) {
            if (this.deathSound != undefined && this.deathSound != "") {
                let es = this.deathSound.split(",")
                for (const s of es) {
                    this.GetParent().EmitSound(s)
                }
            }
        }
    }

    IsDebuff(): boolean {
        return false;
    }

    IsHidden() {
        return true;
    }

    AllowIllusionDuplicate () {
        return true;
    }
    
    RemoveOnDeath(): boolean {
        return true
    }
}
