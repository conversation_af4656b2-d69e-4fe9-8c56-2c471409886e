import { modifier_fw_unit_agi_hero } from "../../modifiers/abilities/unit_innate/modifier_fw_unit_agi_hero";
import { BaseAbility, registerAbility } from "../../utils/dota_ts_adapter";

@registerAbility()
export class fw_unit_agi_hero extends BaseAbility {
    
    GetIntrinsicModifierName(): string {
        return modifier_fw_unit_agi_hero.name
    }

    Precache(context: CScriptPrecacheContext): void {
       
    }
}

