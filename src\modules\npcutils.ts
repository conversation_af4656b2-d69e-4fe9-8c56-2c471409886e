import { modifier_unit_entrance_fall_down } from "../modifiers/entrance/modifier_unit_entrance_fall_down";
import { modifier_healthbar_pip } from "../modifiers/modifier_healthbar_pip";
import { modifier_pre_unit_alternative } from "../modifiers/modifier_pre_unit_alternative";
import { modifier_tower } from "../modifiers/modifier_tower";
import { modifier_tower_be_attack } from "../modifiers/modifier_tower_be_attack";
import { modifier_tower_temp_end } from "../modifiers/modifier_tower_temp_end";
import { modifier_unit_card_duration } from "../modifiers/modifier_unit_card_duration";
import { modifier_unit_sound } from "../modifiers/modifier_unit_sound";
import { modifier_unit_using_delay } from "../modifiers/modifier_unit_using_delay";
import { EntityUtils } from "../utils/entityutils";
import { MathUtils } from "../utils/math_utils";

/**
 * AI逻辑总线核心接口
 * 定义AI系统的基本操作方法
 */
interface IAILogicBus {
    /**
     * 注销单位
     * @param unitId 单位ID
     */
    unregisterUnit(unitId: number): void;
    
    /**
     * 更新AI逻辑
     * @param deltaTime 时间增量
     * @returns 是否需要继续更新
     */
    update(deltaTime: number): boolean;
}

/**
 * NPC工具类 - 游戏AI系统的核心管理器
 * 负责管理所有AI单位的创建、更新、销毁和行为控制
 * 实现了AI逻辑总线接口，提供统一的AI管理服务
 */
export class NPCUtils implements IAILogicBus {
    /** AI思考实体 - 用于定时执行AI更新逻辑 */
    private thinkEntity: CBaseEntity;
    /** 初始化标志 */
    private initFlag = false

    /**
     * 实际在场的单位映射表
     * key: 单位实体索引, value: AI单位对象
     */
    private units: Map<number, IAIUnit> = new Map();
    
    /**
     * 卡牌预加载单位映射表
     * 用于卡牌预加载时保存资源，在卡牌使用后清空
     * key: 卡牌ID, value: 该卡牌对应的AI单位数组
     */
    private cardUnits: Map<number, IAIUnit[]> = new Map();
    
    /** AI单位更新队列 - 用于分帧处理AI逻辑 */
    private updateQueue: IAIUnit[] = [];
    
    /** 脏标记 - 标识更新队列需要重建 */
    private dirtyFlag = false;
    
    /** 当前单位总数 */
    private unitNum = 0
    // private endFlag = false
    // 使用对象池
    // private unitPool: Map<string, CDOTA_BaseNPC[]> = new Map();
    //特效预引用单位
    private uiParUnitEndex:{
        [keys:string]:number
    } = {}
    
    /**
     * 防御塔映射表
     * key: 队伍ID, value: 该队伍的防御塔数组
     */
    public towers: {
        [keys: number]: CDOTA_BaseNPC[]
    } = {}
    
    /**
     * 防御塔实体索引信息表
     * 用于快速查找防御塔相关信息
     */
    private towersEntIndex: {
        [keys: number]: {
            towerIndex: number,     // 防御塔索引(0:左路, 1:右路, 2:基地)
            team: DotaTeam,         // 所属队伍
            playerID: PlayerID,     // 所属玩家ID
            dTimer: number,         // 伤害通知计时器
        }
    } = {}
    
    /**
     * 建筑物映射表
     * key: 队伍ID, value: 该队伍的建筑物实体索引数组
     */
    public buildings: {
        [keys: number]: EntityIndex[]
    } = {}

    /**
     * 构造函数
     * @param maxParallelUpdates 最大并行更新数量，用于性能优化
     */
    constructor(private readonly maxParallelUpdates = 500) {
        // 创建AI思考实体，用于定时执行AI更新
        this.thinkEntity = SpawnEntityFromTableSynchronous("info_target", {
            origin: "0 0 -512",
            scale: "0.1 0.1 0.1",
            targetname: "fast_war_go_think_entity_npc",
            model: "models/empty.vmdl",
        })
        
        let goNPC = this
        // 设置AI思考循环，根据更新结果调整执行频率
        this.thinkEntity.SetThink(() => {
            if (goNPC.update(0.1)) {
                return 0.15  // 正常更新间隔
            }
            return 0.03      // 快速更新间隔
        }, this, "NPCAI", 0.1)
    }

    /**
     * 初始化AI系统
     * 添加必要的处理模块
     */
    init() {
        // 添加处理模块
    }

    /**
     * 清理某个对局的所有单位
     * 目前只支持单对局模式
     */
    public clearUnits() {
        print("清理卡牌")
        let npc: CDOTA_BaseNPC;
        
        // 清理所有战场单位
        Array.from(this.units.values()).forEach((v, index) => {
            npc = EntIndexToHScript(v.entityIndex as EntityIndex) as CDOTA_BaseNPC
            if (IsValidEntity(npc)) {
                UTIL_Remove(npc)
            }
        })
        
        // 清理所有防御塔
        Object.keys(this.towers).forEach((s) => {
            Object.keys(this.towers[s]).forEach((v) => {
                let towerUnit = this.towers[s][v]
                if (IsValidEntity(towerUnit)) {
                    UTIL_Remove(towerUnit)
                }
            })
            this.towers[s] = []
        })
        
        // 重置所有相关数据结构
        this.towersEntIndex = undefined
        this.buildings = {}
        this.units = new Map();
        this.unitNum = 0
        this.markDirty();
    }

    /**
     * 卸载指定卡牌的预加载资源
     * 仅允许清理预载但未置入战场的资源
     * @param cardId 卡牌ID
     */
    public clearCardByID(cardId: number) {
        let units = this.cardUnits.get(cardId)
        units.forEach((v) => {
            v.cardClear()
        })
        this.cardUnits.delete(cardId)
    }


    /**
     * 检查两个队伍的防御塔血量，判断哪方获胜
     * @param p1 队伍1
     * @param p2 队伍2
     * @returns 获胜队伍标识 (1: 队伍1, 2: 队伍2)
     */
    public checkTowerHealthWhoWin(p1: DotaTeam, p2: DotaTeam) {
        let minH = 99999;
        let flag = 1;
        
        // 检查队伍1的防御塔最低血量
        this.towers[p1].forEach((v) => {
            if (IsValidEntity(v) && v.IsAlive && v.IsAlive()) {
                if (v.GetHealth() < minH) {
                    minH = v.GetHealth()
                    flag = 1
                }
            }
        })
        
        // 检查队伍2的防御塔最低血量
        this.towers[p2].forEach((v) => {
            if (IsValidEntity(v) && v.IsAlive && v.IsAlive()) {
                if (v.GetHealth() < minH) {
                    minH = v.GetHealth()
                    flag = 2
                }
            }
        })
        
        return flag;
    }

    public notifierTowerBeAttack (ent:EntityIndex) {
        if (this.towersEntIndex == undefined) {
            this.towersEntIndex = {}
            Object.keys(this.towers).forEach((s)=>{
                Object.keys(this.towers[s]).forEach((v)=>{
                    let towerUnit = this.towers[s][v] as CDOTA_BaseNPC
                    this.towersEntIndex[towerUnit.entindex()] = {
                        towerIndex:towerUnit.towerIndex,
                        team:towerUnit.GetTeam(),
                        playerID:towerUnit.towerPlayerId,
                        dTimer:-1,
                    }
                })
            })
        }
        let p = this.towersEntIndex[ent]
        if (p != undefined) {
            let s = "Fw.UI.Process.Tower.top.beattack"
            if (p.towerIndex == 2) {
                s = "Fw.UI.Process.Tower.bot.beattack"
            } else if (p.towerIndex == 3) {
                s = "Fw.UI.Process.Tower.anc.beattack"
            }
            let time = GameRules.GetGameTime()
            let interval = 10
            if (p.dTimer <= time) {
                let player = PlayerResource.GetPlayer(p.playerID)
                CustomGameEventManager.Send_ServerToPlayer(player, "go_ui_sound",{
                    soundNames:s,
                    group:p.towerIndex,
                    interval:interval,
                }) 
                let particle = ParticleManager.CreateParticleForPlayer(
                    "particles/hud/damage_notification/screen_damage_indicator.vpcf",
                    ParticleAttachment.WORLDORIGIN,
                    undefined,
                    player
                );
                ParticleManager.SetParticleControl(particle, 0, Vector(1,0,0));

                p.dTimer = time + interval
            }
        }
    }

    public sendMessageToUnits (message:AIMessage, AttackType:GoFastWarAttackTypeEnum|undefined, team:DotaTeam|undefined) {
        // print("向队伍"+team+"发送消息！")
        Array.from(this.units.values()).forEach((v,index)=>{
            if (AttackType != undefined && AttackType != v.AttackType) {
                return
            }
            if (team != undefined && v.team != team) {
                return
            }
            // print(v.cardId+"收到消息！")
            v.receiveMessage(message)
        })
    }

    public activeAcient (team:DotaTeam) {
        this.towers[team][2].RemoveModifierByName(modifier_tower_be_attack.name)
    }
   
    public usePreCreatedUnit (tarPos:Vector,cardId:number,cardIndex:number, delay:number, team:DotaTeam, buffs:ModifierData[]) {
        // print("卡牌被使用，ai启动："+cardId+",卡牌index："+cardIndex)
        //播放卡牌使用特效
        let units = this.cardUnits.get(cardId)
        let tarUs:EntityIndex[] = []
        let cardInfo = GameRules.KVUtils.getCardsInfo(cardIndex)
        let poses = MathUtils.calculateLayV(cardInfo.UnitCardInfo.LayType, cardInfo.UnitCardInfo.LayGroupNum, units.map((v)=>v.unitRingRadius), !GameRules.FastWarPhaseController.GameTeamGroup[team].enemyIsGoodGuy)
        // print("是否好阵营："+!GameRules.FastWarPhaseController.GameTeamGroup[team].enemyIsGoodGuy)
        // DeepPrintTable(poses)
        units.forEach((v,index)=>{
            v.cardUnitUsing(tarPos.__add(poses[index]), delay, this.unitNum<this.maxParallelUpdates?1:2, buffs)
            tarUs.push(v.entityIndex as EntityIndex)
            if (v.unitType == GoFastWarAIUnitTypeEnum.BUILDING) {
                this.buildings[team].push(...tarUs)
                this.sendMessageToUnits({type: GoFastWarAIMessageTypeEnum.SET_UNIT_TAR, data:{}},GoFastWarAttackTypeEnum.BUILDING,GameRules.FastWarPhaseController.GameTeamGroup[team].enemyTeamNumber)
            }
        })
        this.cardUnits.delete(cardId)
    }


    public preCreateCardWithUnitName (
        player:CDOTAPlayerController,
        cardId:number,
        cardInfo:CardInfoKV,
        templateInfos:{
            data:UnitTemplateInfoKV,
            unitName:string,
            num:number,
        }[],
        usesNum:number,
    ):string {
        let team = player.GetTeam()
        let res =  ""
        let aiUnits = []
        for (const templateInfo of templateInfos) {
            let resStr = ""
            let avgCost = cardInfo.layCost/templateInfo.num
            let owner = player.GetAssignedHero()
            for (let i = 0 ; i < templateInfo.num ; i++) {
                let npc = this.createUnitWithUnitPool(cardId,templateInfo.unitName, player.GetPlayerID(), owner, team, templateInfo.data.unitType, templateInfo.data.isHealthBarPips, true,templateInfo.data.SpawnActivityModifiers, usesNum)
                resStr = npc.entindex()+"_"+templateInfo.data.modelScale
                let specialU = GameRules.KVUtils.getSpecialUnitInfoUntreated(templateInfo.unitName)
                const unit = new AIUnit(npc.entindex(),cardId, i, 3, team, 
                templateInfo.data.AttackType, avgCost, templateInfo.data.RingRadius,
                templateInfo.data.unitDuration, templateInfo.data.unitType, specialU["EntranceSound"], specialU["DeathSound"], specialU["Entrance"]);


                this.initAIUnit(unit, team, templateInfo.data.unitAI)
                aiUnits.push(unit)
            }
            if (templateInfo.data.ParEntUnit != undefined && templateInfo.data.ParEntUnit != "") {
                resStr = this.createUIParUnit(templateInfo.data.ParEntUnit, player)+"_"+templateInfo.data.modelScale
            }
            res += resStr
            res += ","
        }
        this.cardUnits.set(cardId,aiUnits)
        return res
    }

    private createUIParUnit (uiParUnit:string, player:CDOTAPlayerController) {
        if (this.uiParUnitEndex[uiParUnit] == undefined) {
            let str = GameRules.FastWarPhaseController.GameTeamGroup[player.GetTeam()].enemyIsGoodGuy?"badguys":"goodguys"
            let posAndAngle = GameRules.EntityUtils.EntityOnlyPos["preunit"][str]
            let npc = CreateUnitByName(uiParUnit, posAndAngle.pos, true, undefined, undefined, DotaTeam.NEUTRALS)
            this.uiParUnitEndex[uiParUnit] = npc.entindex()
        }
        // print(uiParUnit+"的引用ent："+this.uiParUnitEndex[uiParUnit])
        return this.uiParUnitEndex[uiParUnit]
    }

    private createUnitWithUnitPool (cardId:number,unitName:string, playerId:PlayerID, owner:CBaseEntity, team:DotaTeam, unitType:GoFastWarAIUnitTypeEnum,isHealthBarPips:boolean,needInvis:boolean, modifiers:string[], usesNum:number):CDOTA_BaseNPC {
        let isGood = !GameRules.FastWarPhaseController.GameTeamGroup[team].enemyIsGoodGuy
        let str = isGood?"goodguys":"badguys"
        let posAndAngle = GameRules.EntityUtils.EntityOnlyPos["preunit"][str]
        // let units = this.unitPool.get(unitName)
        // if (units == undefined) {
        //     units = []
        // }
        // if (units.length > 0) {
        //     for (const u of units) {
        //         print("unitIndex："+u.entindex()+"是否存活："+u.IsAlive())
        //         if (IsValidEntity(u) && !u.fastWarAlive && !u.IsAlive()) {
        //             u.fastWarAlive = true
        //             u.RespawnUnit()
        //             FindClearSpaceForUnit(u, posAndAngle.pos, true)
        //             u.SetOwner(player)
        //             u.SetTeam(team)
        //             print("对象池复用单位："+unitName)
        //             return u
        //         }
        //     }
        // }
        let npc = CreateUnitByName(unitName, posAndAngle.pos, true, owner, owner, team)
        // print("创建单位："+unitName+","+isHealthBarPips)
        if (isHealthBarPips) {
            npc.AddNewModifier(npc, undefined ,modifier_healthbar_pip.name, {})
        }
        if (needInvis) {
            npc.AddNewModifier(undefined, undefined, modifier_pre_unit_alternative.name, {})
        }
        // print("创建单位："+unitName+",id为："+npc.entindex())
        npc.fastWarAlive = true
        npc.fastWarIsGood = isGood
        npc.fastWarUnitType = unitType
        npc.towerIndex = (unitType == GoFastWarAIUnitTypeEnum.BUILDING?0:-1)
        npc.cardId = cardId
        npc.cardUsesNum = usesNum
        npc.SetControllableByPlayer(playerId, true)
        npc.SetAngles(0,posAndAngle.angle.y,0)
        if (modifiers.length > 0) {
            for (const m of modifiers) {
                npc.AddActivityModifier(m)
            }
        }
        // units.push(npc)
        // this.unitPool.set(unitName, units)
        return npc;
    }

    public createUnitForSpell (
        playerId:PlayerID,
        owner:CBaseEntity,
        team:DotaTeam,
        cardId:number,
        avgCost:number,
        templateInfo:UnitTemplateInfoKV,
        num:number,
        unitName:string,
        tarPos:Vector,
        delay:number,
        usesNum:number,
    ) {
        for (let i = 0 ; i < num; i++) {
            let npc = this.createUnitWithUnitPool(cardId,unitName, playerId, owner, team, templateInfo.unitType, templateInfo.isHealthBarPips, false,templateInfo.SpawnActivityModifiers, usesNum)
            let specialU = GameRules.KVUtils.getSpecialUnitInfoTreated(unitName)
            const unit = new AIUnit(npc.entindex(),cardId, i, 3, team, 
            templateInfo.AttackType, avgCost, templateInfo.RingRadius,
            templateInfo.unitDuration, templateInfo.unitType, specialU.EntranceSound, specialU.DeathSound, specialU.Entrance);
            this.initAIUnit(unit, team, templateInfo.unitAI)
            unit.cardUnitUsing(tarPos, delay, this.unitNum<this.maxParallelUpdates?1:2,[])
        }
    }

    public unregisterUnit(unitId: number): void {
        Timers.CreateTimer(3,()=>{
            let unit = (EntIndexToHScript(unitId as EntityIndex) as CDOTA_BaseNPC)
            if (IsValidEntity(unit)) {
                UTIL_Remove(unit)
            }
        })
        this.units.delete(unitId);
        this.unitNum --
        this.markDirty();
    }

    public initAIUnit (aiUnit:AIUnit, team:DotaTeam, unitAI:{
         [keys:number]:{
            autoNum:number,
            spellAI:UnitAIInfoKV[],
        },
    }) {
        aiUnit.addBehavior(new DefaultLayInBehavior());
        if (aiUnit.AttackType == GoFastWarAttackTypeEnum.BUILDING) {
            aiUnit.addBehavior(new BuildingAttackMoveBehavior(team));
        } else if (aiUnit.AttackType == GoFastWarAttackTypeEnum.REVERSE) {
            aiUnit.addBehavior(new BuildingAttackMoveBehavior(team));
        } else {
            aiUnit.addBehavior(new DefaultMoveBehavior(team));
        }
        if (unitAI != undefined) {
            Object.keys(unitAI).forEach((v,index)=>{
                aiUnit.addBehavior(new DefaultSpellCastBehavior(parseInt(v.toString()),unitAI[v].autoNum, unitAI[v].spellAI));
            })
        }
        this.units.set(aiUnit.entityIndex, aiUnit);
        this.markDirty();
        this.unitNum ++
    }

    upIndex:number = 0
    update(deltaTime: number): boolean {
        if (!this.initFlag) {
            this.init()
        }
        if (GameRules.fastWarGameStatus != FastWarGamePhase.MainTime 
            && GameRules.fastWarGameStatus != FastWarGamePhase.Overtime) {
            if (this.upIndex > 0) {
                this.upIndex -= 1
                this.updateUnits(deltaTime);
                return true
            } else {
                return false
            }
        }
        // 分帧更新单位
        this.upIndex = 1
        this.updateUnits(deltaTime);
        return true
    }

   
    private updateUnits(deltaTime: number): void {
        if (this.dirtyFlag) {
            this.rebuildUpdateQueue();
            this.dirtyFlag = false;
        }
        // 分帧处理逻辑
        const start = GameRules.GetGameTime();
        let towerH = [[-1,-1,-1],[-1,-1,-1]]
        Object.keys(this.towers).forEach((v,index)=>{
            let isGood = (!GameRules.FastWarPhaseController.GameTeamGroup[v].enemyIsGoodGuy)?0:1
            let ts:CDOTA_BaseNPC[] = this.towers[v]
            if (ts != undefined) {
                ts.forEach((v,index)=>{
                    if (IsValidEntity(v) && v.IsAlive && v.IsAlive()) {
                        towerH[isGood][index] = v.GetHealth()/v.GetMaxHealth()
                    } else {
                        towerH[isGood][index] = 0
                    }
                })
            }
        })
        
        GameRules.BotGameSituationInfo = {
            towerHealth:towerH,
            halfUnitNum:[[[0,0],[0,0]],[[0,0],[0,0]]],
            halfUnitCost:[[[0,0],[0,0]],[[0,0],[0,0]]],
            halfUnitThreatLevel:[[[0,0],[0,0]],[[0,0],[0,0]]],
            halfUnitHealth:[[[0,0],[0,0]],[[0,0],[0,0]]],
        }
        let processed = 0;
        while (processed < this.maxParallelUpdates) {
            const unit = this.updateQueue.shift();
            if (!unit) break;
            if (unit.dealTime == start) {
                this.updateQueue.push(unit);
                break;
            }
            if (unit.update(start)) {
                unit.dealTime = start
                this.updateQueue.push(unit);
                processed++;
                // 时间切片保护
                if (GameRules.GetGameTime() - start > 100) break;
            } else {
                this.unregisterUnit(unit.entityIndex);
            }
        }
        // let a = GameRules.BotGameSituationInfo.halfUnitNum
        // let b = GameRules.BotGameSituationInfo.halfUnitCost
        // let c = GameRules.BotGameSituationInfo.towerHealth
        // print("在goodGuy部分有good："+a[0][0][0]+","+a[0][0][1]+"个，总费用"+b[0][0][0]+","+b[0][0][1]+"，bad："+a[0][1][0]+","+a[0][1][1]+"个，总费用"+b[0][1][0]+","+b[0][1][1]+"。")
        // print("good防御塔：左"+c[0][0]+"血，右"+c[0][1]+"血，基地："+c[0][2]+"血。")
        // print("在badGuy部分有good："+a[1][0][0]+","+a[1][0][1]+"个，总费用"+b[1][0][0]+","+b[1][0][1]+"，bad："+a[1][1][0]+","+a[1][1][1]+"个，总费用"+b[1][1][0]+","+b[1][1][1]+"。")
        // print("bad防御塔：左"+c[1][0]+"血，右"+c[1][1]+"血，基地："+c[1][2]+"血。")

        // print("在开始时间为："+start+"的循环中processed为"+processed)
    }
    private rebuildUpdateQueue(): void {
        this.updateQueue = Array.from(this.units.values())
            .sort((a, b) => b.priority - a.priority);
    }
    private markDirty(): void {
        this.dirtyFlag = true;
    }

    public tempEnd () {
        Object.keys(this.towers).forEach((s)=>{
            Object.keys(this.towers[s]).forEach((v)=>{
                let towerUnit = this.towers[s][v]
                towerUnit.AddNewModifier(v, undefined ,modifier_tower_temp_end.name, {})
            })
        })
    }

    public CreateTower(towerIndex:number, isGood:boolean, pos:Vector, angle:QAngle, template:string, team:DotaTeam, player?:CDOTAPlayerController):CDOTA_BaseNPC_Building {
        pos = GetGroundPosition(pos, undefined)
        let posStr = pos.x + " " + pos.y + " " + pos.z
        let angleStr = angle.x + " " + angle.y + " " + angle.z

        if (player == undefined) {
            player = PlayerResource.GetPlayer(GameRules.PlayerIDs[0])
        }
        let lvl1 = team==DotaTeam.GOODGUYS?"particles/radiant_fx2/radiant_tower002_dest_lvl1.vpcf":"particles/dire_fx/dire_tower002_dest_lvl1.vpcf"
        let lvl2 = team==DotaTeam.GOODGUYS?"particles/radiant_fx2/radiant_tower002_dest_lvl2.vpcf":"particles/dire_fx/dire_tower002_dest_lvl2.vpcf"

        let unitName = GameRules.PlayerData.getPlayerSpecialCardInfo(player.GetPlayerID(), template, team)
        let specialUnitInfo = GameRules.KVUtils.getSpecialUnitInfoUntreated(unitName)
        let templateInfo = GameRules.KVUtils.getUnitTemplateInfoUntreated(template)

        let v = SpawnEntityFromTableSynchronous("npc_dota_tower", {
            origin : posStr,
            angles : angleStr,
            model : specialUnitInfo["Model"],
            skin : "0",
            bodygroups : "",
            teamnumber : team.toString(),
            playerowner : player.GetPlayerID().toString(),

            MapUnitName : unitName,
            "Ability1"			:		templateInfo["Ability1"]	,		// Ability 1.
            "Ability2"			:		templateInfo["Ability2"]	,		// Ability 2.
            "Ability3"			:		templateInfo["Ability3"]	,		// Ability 3.

            // destruction_lvl1 : "models/props_structures/default/dire_tower002_dest_lvl1.vmdl",
            destdmgamnt_lvl1 : "66",
            damagefx_lvl1 : lvl1,
            // destruction_lvl2 : "models/props_structures/default/dire_tower002_dest_lvl2.vmdl",
            destdmgamnt_lvl2 : "33",
            damagefx_lvl2 : lvl2,
            ambientfx : specialUnitInfo["TowerAmbientPar"],
            destroyfx : specialUnitInfo["TowerDestroyPar"],


            scales : "1 1 1",
            "transform locked" : "0",
            "force hidden" : "0",
            "editor only" : "0",
            targetName : unitName,
            disableshadows : "0",
            renderfx : "0",
            rendermode : "0",
            renderamt : "255",
            disablereceiveshadows : "0",
            fademindist : "-1",
            fademaxdist : "0",
            object_culling : "0",
            rendertocubemaps : "0",
            lightmapstatic : "0",
            rendercolor : "255 255 255",
            link : "",
            vulnerableoncreepspawn : "0",
            invuln_count : "0",
            particle_tint_color : "255 255 255",
            destroysound : "Building_DireTower.Destruction",
            dmglvl1sound : "Building_Generic.PartialDestruction",
            dmglvl2sound : "Building_Generic.Destruction",
            "AutoAttacksByDefault": "1",

            "BaseClass"			:		"npc_dota_tower",	// Class of entity of link to.
            "SoundSet"				:	"Tower.Water",					// Name of sound set.
            "MinimapIcon"			:	"minimap_tower90",
            "MinimapIconSize"		:	"320",
            "Level"					:	"1",
            "wearable"				:	"",

            // Armor
            //----------------------------------------------------------------
            "ArmorPhysical"			:	templateInfo["ArmorPhysical"]	,	// Physical protection.
            "MagicalResistance"		:	templateInfo["MagicalResistance"]	,		// Magical protection.
            "AttackDamageMin"		:	templateInfo["AttackDamageMin"]	,	// Damage range min.
            "AttackDamageMax"		:	templateInfo["AttackDamageMax"]	,	// Damage range max.
            // Attack
            //----------------------------------------------------------------
            "AttackCapabilities"	:	templateInfo["AttackCapabilities"],
            "AttackDamageType"		:	templateInfo["AttackDamageType"],
            "BaseAttackSpeed"		:	templateInfo["BaseAttackSpeed"],
            "AttackRate"			:	templateInfo["AttackRate"],	// Speed of attack.
            "AttackAnimationPoint"	:	templateInfo["AttackAnimationPoint"],	// Normalized time in animation cycle to attack.
            "AttackAcquisitionRange":	templateInfo["AttackAcquisitionRange"],	// Range within a target can be acquired.
            "AttackRange"			:	templateInfo["AttackRange"],	// Range within a target can be attacked.
            "ProjectileModel"		:	specialUnitInfo["ProjectileModel"],	// Particle system model for projectile.
            "ProjectileSpeed"		:	templateInfo["ProjectileSpeed"],	// Speed of projectile.
            // Bounty
            //----------------------------------------------------------------
            "BountyGoldMin"			:	"0"	,	// Gold earned min.
            "BountyGoldMax"			:	"0"	,	// Gold earned max.
            // Bounds
            //----------------------------------------------------------------
            "BoundsHullName"		:	"DOTA_HULL_SIZE_TOWER"	,		// Hull type used for navigation/locomotion.
            "ProjectileCollisionSize":	"50",
            "RingRadius"			:	templateInfo["RingRadius"],
            "HealthBarOffset"		:	templateInfo["HealthBarOffset"],
            // Movement
            //----------------------------------------------------------------
            "MovementCapabilities"	:	"DOTA_UNIT_CAP_MOVE_NONE"	,		// Type of locomotion - ground, air
            "MovementSpeed"			:	"0"	,		// Speed
            "MovementTurnRate"		:	"1.0"	,		// Turning rate.
            // Status
            //----------------------------------------------------------------
            "StatusHealth"			:	templateInfo["StatusHealth"],	// Base health.
            "StatusHealthRegen"		:	templateInfo["StatusHealthRegen"],	// Health regeneration rate.
            "StatusMana"			:	templateInfo["StatusMana"],	// Base mana.
            "StatusManaRegen"		:	templateInfo["StatusManaRegen"],	// Mana regeneration rate.
            // Team
            //----------------------------------------------------------------
            "UnitRelationshipClass"	:	"DOTA_NPC_UNIT_RELATIONSHIP_TYPE_BUILDING",
            // Vision
            //----------------------------------------------------------------
            "VisionDaytimeRange"	:	"2000"	,	// Range of vision during day light.
            "VisionNighttimeRange"	:	"2000"	,	// Range of vision at night time.
            "AttackRangeActivityModifiers":
            {
                "attack_normal_range":	"0",
                "attack_long_range"	:	"500",
            }
        }) as CDOTA_BaseNPC
        DoEntFireByInstanceHandle(v,"SetInvulnCount","0",0,null,null)
        if (v.HasModifier("modifier_tower_aura")) {
            v.RemoveModifierByName("modifier_tower_aura")
        }
        v.AddNewModifier(v, undefined ,modifier_tower.name, {bounsRange:templateInfo["RingRadius"]})
        if (towerIndex == 3) {
            v.AddNewModifier(v, undefined ,modifier_tower_be_attack.name, {})
        }
        let buffs = GameRules.FastWarPlayerHeroSpell.getCardUsingBuff(player.GetPlayerID(), PlayerHeroSpellFunction.TOWER_BUFF, {towerIndex:towerIndex})
        for (const buff of buffs) {
            // print("添加buff："+buff.modifierName)
            v.AddNewModifier(buff.caster, buff.ab, buff.modifierName, buff.data)
        }

        v.SetControllableByPlayer(1, true)
        v.towerIndex = towerIndex
        v.fastWarUnitType = GoFastWarAIUnitTypeEnum.BUILDING
        v.fastWarIsGood = isGood
        v.towerPlayerId = player.GetPlayerID()
        if (this.towers[team] == undefined) {
            this.towers[team] = []
        }
        if (this.buildings[team] == undefined) {
            this.buildings[team] = []
        }
        this.towers[team].push(v as CDOTA_BaseNPC)
        this.buildings[team].push(v.entindex())
        return v as CDOTA_BaseNPC_Building
    }
}


/**
 * AI单位基础接口
 * 定义AI单位必须实现的基本方法
 */
interface IAIUnit {
    /** 实体索引 */
    readonly entityIndex: number;
    /** 卡牌ID */
    readonly cardId: number,
    /** 单位环形半径 */
    readonly unitRingRadius: number,
    /** 单位类型 */
    readonly unitType: number,
    /** 优先级 */
    priority: number;
    /** 处理时间戳 */
    dealTime: number;
    /** 所属队伍 */
    team: DotaTeam,
    /** 攻击类型 */
    AttackType: GoFastWarAttackTypeEnum,
    
    /**
     * 卡牌单位使用
     * @param tarPos 目标位置
     * @param delay 延迟时间
     * @param priority 优先级
     * @param buffs 增益效果数组
     */
    cardUnitUsing(tarPos: Vector, delay: number, priority: number, buffs: ModifierData[]): boolean;
    
    /** 卡牌清理 */
    cardClear(): void;
    
    /**
     * 更新逻辑
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): boolean;
    
    /**
     * 接收消息
     * @param message AI消息
     */
    receiveMessage(message: AIMessage): void;
}

/**
 * AI单位实现类
 * 实现了IAIUnit接口，提供完整的AI单位功能
 */
class AIUnit implements IAIUnit {
    /** 行为列表 */
    private behaviors: AIBehavior[] = [];
    /** 消息队列 */
    private messageQueue: AIMessage[] = [];
    /** 单位状态 */
    private unitStatus: boolean = false
    /** 处理时间戳 */
    public dealTime = 0;

    /**
     * 构造函数
     * @param entityIndex 实体索引
     * @param cardId 卡牌ID
     * @param cardUnitIndex 卡牌单位索引
     * @param priority 优先级
     * @param team 队伍
     * @param AttackType 攻击类型
     * @param cardUnitAvgCost 卡牌单位平均费用
     * @param unitRingRadius 单位环形半径
     * @param unitDuration 单位持续时间
     * @param unitType 单位类型
     * @param entranceSound 入场音效
     * @param deathSound 死亡音效
     * @param entranceType 入场类型
     */
    constructor(
        public readonly entityIndex: number,
        public readonly cardId: number,
        public readonly cardUnitIndex: number,
        public priority: number,
        public team: DotaTeam,
        public AttackType: GoFastWarAttackTypeEnum,
        public readonly cardUnitAvgCost: number,
        public readonly unitRingRadius: number,
        public readonly unitDuration: number,
        public readonly unitType: number,
        public readonly entranceSound: string,
        public readonly deathSound: string,
        public readonly entranceType: string,
    ) { }

    /**
     * 卡牌清理
     * 移除单位实体
     */
    cardClear() {
        let unit = EntIndexToHScript(this.entityIndex as EntityIndex) as CDOTA_BaseNPC;
        if (IsValidEntity(unit)) {
            UTIL_Remove(unit)
        }
    }

    /**
     * 卡牌单位使用
     * 激活单位并设置相关参数
     * @param tarPos 目标位置
     * @param delay 延迟时间
     * @param priority 优先级
     * @param buffs 增益效果数组
     */
    cardUnitUsing(tarPos: Vector, delay: number, priority: number, buffs: ModifierData[]) {
        this.priority = priority
        let q = GameRules.FastWarPhaseController.GameTeamGroup[this.team].enemyIsGoodGuy ? 180 : 0
        
        // 发送启动位置消息
        this.receiveMessage({
            type: GoFastWarAIMessageTypeEnum.START_POS,
            data: {
                pos: tarPos,
                angleYaw: q,
                delay: delay,
                unitDuration: this.unitDuration,
                entranceType: this.entranceType,
                entranceSound: this.entranceSound,
                deathSound: this.deathSound,
                buffs: buffs,
            },
        })
        
        // 发送设置目标消息
        this.receiveMessage({
            type: GoFastWarAIMessageTypeEnum.SET_UNIT_TAR,
            data: {},
        })
        return true
    }

    /**
     * 添加行为
     * @param behavior AI行为对象
     */
    addBehavior(behavior: AIBehavior): void {
        this.behaviors.push(behavior);
    }
  
    update(deltaTime: number): boolean {
        if (this.priority > 2) {
            return true
        }
        let unit =  EntIndexToHScript(this.entityIndex as EntityIndex) as CDOTA_BaseNPC;
        // print("刷新单位状态："+IsValidEntity(unit)+","+unit.IsAlive())
        if (IsValidEntity(unit) && unit.IsAlive && unit.IsAlive()) {
            let selfG = !GameRules.FastWarPhaseController.GameTeamGroup[unit.GetTeam()].enemyIsGoodGuy
            let isGood = selfG?0:1
            let atGood = unit.GetAbsOrigin().y<0?0:1
            let LOrR = MathUtils.getLOrR(selfG,unit)
            GameRules.BotGameSituationInfo.halfUnitNum[atGood][isGood][LOrR] += 1
            GameRules.BotGameSituationInfo.halfUnitCost[atGood][isGood][LOrR] += this.cardUnitAvgCost
            GameRules.BotGameSituationInfo.halfUnitThreatLevel[atGood][isGood][LOrR] += this.cardUnitAvgCost*(Math.max(Math.abs(unit.GetAbsOrigin().y), 300)/1500) * (unit.GetHealth() / unit.GetMaxHealth())
            GameRules.BotGameSituationInfo.halfUnitHealth[atGood][isGood][LOrR] += unit.GetHealth()

            this.processMessages(unit);
            this.behaviors.forEach(b => {
                let m = b.execute(unit, deltaTime)
                if (m != undefined) {
                   this.receiveMessage(m)
                }
            });
            return true
        }
        return false
    }

    receiveMessage(message: AIMessage): void {
        if (this.priority > 2) {
            return
        }
        // print(this.cardId+"添加："+message.type)
        this.messageQueue.push(message);
    }
  
    private processMessages(unit:CDOTA_BaseNPC): void {
        while (this.messageQueue.length > 0) {
            const msg = this.messageQueue.shift()!;
            // print(this.cardId+"处理消息："+msg.type)
            this.behaviors.forEach(b => b.handleMessage(unit,msg));
        }
    }
}

/**
 * AI行为基类
 * 定义AI行为的基本接口
 */
abstract class AIBehavior {
    /**
     * 执行行为逻辑
     * @param unit 单位实体
     * @param deltaTime 时间增量
     * @returns 可能产生的AI消息
     */
    abstract execute(unit: CDOTA_BaseNPC, deltaTime: number): AIMessage | undefined;
    
    /**
     * 处理消息
     * @param unit 单位实体
     * @param message AI消息
     */
    abstract handleMessage(unit: CDOTA_BaseNPC, message: AIMessage): void;
}

/**
 * AI消息类型定义
 * 用于AI系统内部通信
 */
type AIMessage = {
    /** 消息类型 */
    type: GoFastWarAIMessageTypeEnum;
    /** 消息数据 */
    data: {
        pos?: Vector,                    // 位置
        angleYaw?: number,               // 偏航角
        delay?: number,                  // 延迟时间
        unitDuration?: number,           // 单位持续时间
        entranceType?: string,           // 入场类型
        tarUnits?: EntityIndex[],        // 目标单位数组
        abIndex?: number,                // 技能索引
        entranceSound?: string,          // 入场音效
        deathSound?: string,             // 死亡音效
        buffs?: ModifierData[],          // 增益效果数组
    }
};
/**
 * 默认技能施法行为
 * 处理单位的自动技能释放逻辑
 */
class DefaultSpellCastBehavior extends AIBehavior {
    /** 技能对象 */
    ab: CDOTABaseAbility;

    /**
     * 构造函数
     * @param index 技能索引
     * @param usingNum 使用次数
     * @param botAIInfoKVs 机器人AI信息配置数组
     */
    constructor(
        private index: number,
        private usingNum: number,
        private botAIInfoKVs: UnitAIInfoKV[],
    ) {
        super();
    }

    /**
     * 执行技能施法逻辑
     * @param unit 单位实体
     * @param deltaTime 时间增量
     * @returns 可能产生的使用技能消息
     */
    execute(unit: CDOTA_BaseNPC, deltaTime: number): AIMessage {
        if (this.usingNum == 0 || !IsValidEntity(unit) || !unit.IsAlive() || unit.IsSilenced() || unit.IsStunned()) {
            return
        }
        let ab = unit.GetAbilityByIndex(this.index-1)
        if (ab != undefined && ab.IsActivated() && ab.IsCooldownReady() 
            && !ab.IsHidden() && !ab.IsInAbilityPhase() && !ab.IsChanneling() && ab.IsOwnersManaEnough()) {
            for (let botAIInfoKV of  this.botAIInfoKVs) {
                if (botAIInfoKV.SelfFilterType) {//自身条件判定
                    if (botAIInfoKV.SelfFilter.Health != undefined
                        && !MathUtils.healthCheck(botAIInfoKV.SelfFilter.Health,unit.GetHealth(), unit.GetMaxHealth())) {
                        continue
                    }
                    if (botAIInfoKV.SelfFilter.PosY != undefined) {
                        let selfGood = !GameRules.FastWarPhaseController.GameTeamGroup[unit.GetTeam()].enemyIsGoodGuy
                        let y = unit.GetAbsOrigin().y
                        if ((selfGood && y > 0) || (!selfGood && y < 0)) {//过了河道
                            if (botAIInfoKV.SelfFilter.PosY < 0) {//判断不过河道
                                continue 
                            }
                        } else {
                            if (botAIInfoKV.SelfFilter.PosY > 0) {
                                continue 
                            }
                        }
                    }
                }
                let tars:CDOTA_BaseNPC[] = []
                if (botAIInfoKV.FindTarType == 0) {//不搜索目标
                    tars = [unit]
                } else if (botAIInfoKV.FindTarType == 1) {
                    tars = FindUnitsInRadius(
                        unit.GetTeam(),
                        unit.GetAbsOrigin(),
                        undefined,
                        botAIInfoKV.FindRadiusP.Radius,
                        botAIInfoKV.FindRadiusP.TargetTeam,
                        botAIInfoKV.FindRadiusP.UnitTargetType,
                        botAIInfoKV.FindRadiusP.UnitTargetFlags,
                        botAIInfoKV.FindRadiusP.FindOrder,
                        false,
                    )
                    if (tars.length <= 0) {
                        continue
                    }
                    if (botAIInfoKV.FindRadiusP.UnitTargetType as any == 19) {
                        tars = tars.filter((v)=>v.fastWarUnitType != GoFastWarAIUnitTypeEnum.BUILDING)
                    }
                }
                if (botAIInfoKV.TarFilterType) {
                    tars = [...tars.filter((e)=>{
                        if (botAIInfoKV.TarFilter.Health != undefined
                            && !MathUtils.healthCheck(botAIInfoKV.TarFilter.Health,e.GetHealth(), e.GetMaxHealth())) {
                                return false
                        }
                        if (botAIInfoKV.TarFilter.FindSelf == 0 && e.entindex() == unit.entindex()) {
                            return false
                        }
                        return true
                    })]
                    if (tars.length <= 0) {
                        continue
                    }
                }
                if (botAIInfoKV.AfterFilter.Num != undefined && !MathUtils.numCheck(botAIInfoKV.AfterFilter.Num, tars.length)) {
                    continue
                }
                let order:ExecuteOrderOptions = {
                    UnitIndex:unit.entindex(),
                    OrderType:botAIInfoKV.CastType,
                    AbilityIndex:ab.entindex(),
                    Queue:false,
                }
                let oneTar;
                if (botAIInfoKV.AfterFilter.OnlySelf) {
                    oneTar = unit
                } else {
                    if (botAIInfoKV.AfterFilter.HealthDeal != 0) { 
                        tars.sort((a,b)=>{
                            if (botAIInfoKV.AfterFilter.HealthDeal == 1) {
                                return b.GetHealth() - a.GetHealth()
                            } else {
                                return a.GetHealth() - b.GetHealth()
                            }
                        })
                    }
                    if (botAIInfoKV.AfterFilter.ModifierFilter.length > 0) {
                        tars.sort((a,b)=>{
                            for (const buff of botAIInfoKV.AfterFilter.ModifierFilter) {
                                return (b.HasModifier(buff)?0:1) - (a.HasModifier(buff)?0:1)
                            }
                        })
                    }
                    oneTar = tars[0]
                }
                
                switch (botAIInfoKV.CastType) {
                    case UnitOrder.CAST_POSITION:
                        if (botAIInfoKV.AfterFilter.RePos) {
                            order.Position = MathUtils.computeCentroid(tars.map((e)=>e.GetAbsOrigin()))
                        } else {
                            order.Position = oneTar.GetAbsOrigin()
                        }
                        break;
                    case UnitOrder.CAST_TARGET:
                        order.TargetIndex = oneTar.entindex()
                        break;
                    case UnitOrder.CAST_TARGET_TREE:
                        let trees = GridNav.GetAllTreesAroundPoint(unit.GetAbsOrigin(),2000,false);
                        order.TargetIndex = GetTreeIdForEntityIndex(trees[0].entindex()) as EntityIndex
                        GameRules.treeIndex++
                        break;
                }
                // DeepPrintTable(order)
                // if (botAIInfoKV.AfterFilter.Delay != undefined && botAIInfoKV.AfterFilter.Delay > 0) {
                //     this.delayOrder = order
                //     this.delayOrderTime = GameRules.GetGameTime() + botAIInfoKV.AfterFilter.Delay
                //     return
                // } else {
                    ExecuteOrderFromTable(order)
                    this.usingNum -= 1
                    return {type: GoFastWarAIMessageTypeEnum.USE_SPELL, data:{abIndex:ab.GetAbilityIndex()}}
                // }
            }
        }
    }

    handleMessage(unit:CDOTA_BaseNPC, message: AIMessage): void {
        
    }
}

/**
 * 默认布局行为
 * 处理单位的初始化和入场逻辑
 */
class DefaultLayInBehavior extends AIBehavior {

    /**
     * 执行逻辑（布局行为通常不需要持续执行）
     * @param unit 单位实体
     * @param deltaTime 时间增量
     */
    execute(unit: CDOTA_BaseNPC, deltaTime: number) {
        return undefined
    }

    /**
     * 处理消息
     * @param unit 单位实体
     * @param message AI消息
     */
    handleMessage(unit: CDOTA_BaseNPC, message: AIMessage): void {
        if (message.type == GoFastWarAIMessageTypeEnum.START_POS) {
            // print("处理消息：初始化启动")
            unit.RemoveModifierByName(modifier_pre_unit_alternative.name)
            unit.StartGestureWithFade(GameActivity.DOTA_SPAWN, 0.5, 0.5);
            if (message.data.entranceType == "fall_down") {
                unit.AddNewModifier(unit, undefined, modifier_unit_entrance_fall_down.name, {duration:0.4,posX:message.data.pos.x,posY:message.data.pos.y, delayDuration:message.data.delay, unitDuration:message.data.unitDuration})
            } else {
                FindClearSpaceForUnit(unit,message.data.pos, true)
                if (message.data.delay > 0) {
                    // unit.SetAngles(0,message.data.angleYaw,0)
                    unit.AddNewModifier(unit, undefined, modifier_unit_using_delay.name, {duration:message.data.delay, unitDuration:message.data.unitDuration})
                } else {
                    if (message.data.unitDuration > 0) {
                        unit.AddNewModifier(unit, undefined, modifier_unit_card_duration.name, {duration:message.data.unitDuration+1,fastWarDuration:message.data.unitDuration})
                    }   
                }
            }
            unit.AddNewModifier(unit, undefined, modifier_unit_sound.name, {entranceSound:message.data.entranceSound,deathSound:message.data.deathSound})
            if (message.data.buffs != undefined && message.data.buffs.length > 0) {
                for (const buff of message.data.buffs) {
                    // print("添加buff："+buff.modifierName)
                    unit.AddNewModifier(buff.caster, buff.ab, buff.modifierName, buff.data)
                }
            }
            
        }
    }
}
/**
 * 默认移动行为
 * 处理普通单位的移动和攻击逻辑
 */
class DefaultMoveBehavior extends AIBehavior {
    /** 目标位置 */
    tarPos: Vector;
    /** 目标建筑 */
    tarBuilding: CDOTA_BaseNPC;
    /** 当前单位位置 */
    nowUnitPos: Vector;
    /** 技能缓存 */
    abs: {
        [keys: number]: CDOTABaseAbility
    } = {};
    /** 最后一次指令缓存 */
    lastqueue: {
        OrderType: UnitOrder,
        TargetIndex: EntityIndex,
        Position: Vector,
    } = {
            OrderType: UnitOrder.ATTACK_MOVE,
            TargetIndex: -1 as EntityIndex,
            Position: Vector(0, 0, 0),
        }

    /**
     * 构造函数
     * @param team 所属队伍
     */
    constructor(private team: DotaTeam) {
        super();
    }

    /**
     * 执行移动和攻击逻辑
     * @param unit 单位实体
     * @param deltaTime 时间增量
     */
    execute(unit: CDOTA_BaseNPC, deltaTime: number): AIMessage {
        if (this.nowUnitPos == undefined) {
            this.nowUnitPos = unit.GetAbsOrigin()
        }
        // print("执行动作")
        if (GameRules.fastWarGameStatus != FastWarGamePhase.MainTime && GameRules.fastWarGameStatus != FastWarGamePhase.Overtime) {
            ExecuteOrderFromTable({
                UnitIndex:unit.entindex(),
                OrderType:UnitOrder.HOLD_POSITION,
            })
        } else {
            if (unit.IsDisarmed() //被缴械
                || Object.values(this.abs).filter((v)=>{v.IsInAbilityPhase()}).length > 0 // 正在施法
                || unit.IsChanneling() //正在施法
                || unit.HasModifier("modifier_spirit_breaker_charge_of_darkness") // 特殊处理白牛
            ) {
                // print("跳过处理")
            } else {
                if (this.nowUnitPos.__sub(unit.GetAbsOrigin()).Length2D() > 120 //0.2秒内位移超过120码
                    || this.tarBuilding == undefined || !IsValidEntity(this.tarBuilding) || !this.tarBuilding.IsAlive() //当前未锁定攻击建筑时
                ) {
                    this.dealTarPos(unit)
                }
                if ((unit.GetAttackCapability() != UnitAttackCapability.NO_ATTACK && !unit.IsAttacking()) 
                    && !unit.IsMoving() && !unit.IsStunned()) {
                    //保险机制，当单位没被控制且没攻击，没移动时，重新下达指令
                    this.lastqueue = {
                        OrderType:UnitOrder.ATTACK_MOVE,
                        TargetIndex:-1 as EntityIndex,
                        Position:Vector(0,0,0),
                    }
                }
                if (this.tarBuilding == undefined) {
                    if (this.lastqueue.OrderType != UnitOrder.ATTACK_MOVE || this.lastqueue.Position.__sub(this.tarPos).Length2D() > 30) {
                        ExecuteOrderFromTable({
                            UnitIndex:unit.entindex(),
                            OrderType:UnitOrder.ATTACK_MOVE,
                            Position:this.tarPos,
                            Queue:false,
                        })  
                        this.lastqueue.OrderType = UnitOrder.ATTACK_MOVE
                        this.lastqueue.Position = this.tarPos
                    }
                } else {
                    if (this.lastqueue.OrderType != UnitOrder.ATTACK_TARGET || this.lastqueue.TargetIndex != this.tarBuilding.entindex()) {
                        ExecuteOrderFromTable({
                            UnitIndex:unit.entindex(),
                            OrderType:UnitOrder.ATTACK_TARGET,
                            TargetIndex:this.tarBuilding.entindex(),
                            Queue:false,
                        })  
                        this.lastqueue.OrderType = UnitOrder.ATTACK_TARGET
                        this.lastqueue.TargetIndex = this.tarBuilding.entindex()
                    }
                }
            }
        }
        this.nowUnitPos = unit.GetAbsOrigin()
        return undefined
    }
    handleMessage(unit:CDOTA_BaseNPC, message: AIMessage): void {
        if (message.type == GoFastWarAIMessageTypeEnum.USE_SPELL) {
            if (message.data.abIndex != undefined ) {
                if (this.abs[message.data.abIndex] == undefined) {
                    let ab = unit.GetAbilityByIndex(message.data.abIndex)
                    if (ab != undefined) {
                        this.abs[message.data.abIndex] = ab
                    }
                }
                this.lastqueue = {
                    OrderType:UnitOrder.ATTACK_MOVE,
                    TargetIndex:-1 as EntityIndex,
                    Position:Vector(0,0,0),
                }
            }
        }
    }
    dealTarPos (unit:CDOTA_BaseNPC) {
        let enemyTeamInfo = GameRules.FastWarPhaseController.GameTeamGroup[this.team];
        let LOrR = MathUtils.getLOrR(enemyTeamInfo.enemyIsGoodGuy,unit)
        //默认先选左路或右路防御塔
        let tower = GameRules.NPCUtils.towers[enemyTeamInfo.enemyTeamNumber][LOrR]
        let font = GameRules.NPCUtils.towers[enemyTeamInfo.enemyTeamNumber][2]
        if (tower == undefined || !IsValidEntity(tower) || !tower.IsAlive()) {
            tower = font
        } else { //一塔存活
            tower = tower.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D() < font.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D() ? tower: font
        }

        let pos = unit.GetAbsOrigin()
        let tarPos = tower.GetAbsOrigin()
        let l = tarPos.__sub(pos).Length2D()
        // print("当前和敌方建筑距离为："+l+",攻击距离为："+unit.Script_GetAttackRange())
        if (l <= unit.Script_GetAttackRange()+200) {
            let tars = FindUnitsInRadius(
                unit.GetTeam(),
                pos,
                undefined,
                l,
                UnitTargetTeam.ENEMY,
                UnitTargetType.HERO,
                UnitTargetFlags.NONE,
                FindOrder.CLOSEST,
                false,
            )
            if (tars.length <= 0) {
                // print("锁定敌方防御塔进行攻击！")
                this.tarPos = undefined
                this.tarBuilding = tower
                return 
            }
        }
        // print("无建筑目标，a地板")
        this.tarPos = tarPos
        this.tarBuilding = undefined
    }
}
/**
 * 建筑攻击移动行为
 * 专门用于只攻击建筑的单位（如攻城单位）
 */
class BuildingAttackMoveBehavior extends AIBehavior {
    /** 目标单位 */
    tarU: CDOTA_BaseNPC;
    /** 技能缓存 */
    abs: {
        [keys: number]: CDOTABaseAbility
    } = {};
    /** 最后一次指令缓存 */
    lastqueue: {
        OrderType: UnitOrder,
        TargetIndex: EntityIndex,
    } = {
            OrderType: UnitOrder.HOLD_POSITION,
            TargetIndex: -1 as EntityIndex,
        }

    /**
     * 构造函数
     * @param team 所属队伍
     */
    constructor(
        private team: DotaTeam,
    ) {
        super();
    }

    /**
     * 执行建筑攻击逻辑
     * @param unit 单位实体
     * @param deltaTime 时间增量
     */
    execute(unit: CDOTA_BaseNPC, deltaTime: number) {
        if (GameRules.fastWarGameStatus != FastWarGamePhase.MainTime && GameRules.fastWarGameStatus != FastWarGamePhase.Overtime) {
            ExecuteOrderFromTable({
                UnitIndex:unit.entindex(),
                OrderType:UnitOrder.HOLD_POSITION,
                Queue:false,
            })
        } else {
            if (unit.IsDisarmed() 
                || unit.IsStunned()
                || Object.values(this.abs).filter((v)=>{v.IsInAbilityPhase()}).length > 0
                || unit.IsChanneling() //正在施法
            ) {

            } else {
                if (this.tarU == undefined || !IsValidEntity(this.tarU) || !this.tarU.IsAlive() 
                    || this.tarU.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D() > unit.Script_GetAttackRange()) {
                    this.dealTar(unit)
                }
                if (!unit.IsAttacking() && !unit.IsMoving() && !unit.IsStunned()) {
                    //保险机制，当单位没被控制且没攻击，没移动时，重新下达指令
                    this.lastqueue = {
                        OrderType:UnitOrder.HOLD_POSITION,
                        TargetIndex:-1 as EntityIndex,
                    }
                }
                let tar = unit.GetAttackTarget()
                if (tar != undefined && tar.IsAlive() && tar.towerIndex < 0) {
                    //保险机制，当监测到攻击对象不符合条件，重新下达指令
                    // print("监测到攻击对象不符合条件！")
                    this.tarU = undefined
                    this.lastqueue = {
                        OrderType:UnitOrder.HOLD_POSITION,
                        TargetIndex:-1 as EntityIndex,
                    }
                    ExecuteOrderFromTable({
                        UnitIndex:unit.entindex(),
                        OrderType:UnitOrder.HOLD_POSITION,
                        Queue:false,
                    })
                } else if (tar == undefined || !tar.IsAlive() || tar.entindex() != this.tarU.entindex()) {
                    // print("更新攻击对象！")
                    if (this.lastqueue.OrderType != UnitOrder.ATTACK_TARGET || this.lastqueue.TargetIndex != this.tarU.entindex()) {
                        ExecuteOrderFromTable({
                            UnitIndex:unit.entindex(),
                            OrderType:UnitOrder.ATTACK_TARGET,
                            TargetIndex:this.tarU.entindex(),
                            Queue:false,
                        })
                        this.lastqueue = {
                            OrderType:UnitOrder.ATTACK_TARGET,
                            TargetIndex:this.tarU.entindex(),
                        }
                    }
                }
            }
        }
        return undefined
    }
    handleMessage(unit:CDOTA_BaseNPC, message: AIMessage): void {
        if (message.type == GoFastWarAIMessageTypeEnum.SET_UNIT_TAR) {
            // let l = this.tarU.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D()
            // if (this.tarU == undefined || !IsValidEntity(this.tarU) || !this.tarU.IsAlive() || l > unit.Script_GetAttackRange()) {
            //     this.dealTar(unit)
            // }
        } else if (message.type == GoFastWarAIMessageTypeEnum.USE_SPELL) {
            if (message.data.abIndex != undefined ) {
                if (this.abs[message.data.abIndex] == undefined) {
                    let ab = unit.GetAbilityByIndex(message.data.abIndex)
                    if (ab != undefined) {
                        this.abs[message.data.abIndex] = ab
                    }
                }
                this.lastqueue = {
                    OrderType:UnitOrder.HOLD_POSITION,
                    TargetIndex:-1 as EntityIndex,
                }
            }
        }
    }
    private dealTar (unit:CDOTA_BaseNPC) {
        // print("********************************************")
        // print("处理"+unit.GetUnitName()+"仅攻击建筑目标筛选：")
        let enemyTeamInfo = GameRules.FastWarPhaseController.GameTeamGroup[this.team];
        let vaildBuilding:EntityIndex[] = []

        let LOrR = MathUtils.getLOrR(enemyTeamInfo.enemyIsGoodGuy,unit)
        //默认先选左路或右路防御塔
        let tower = GameRules.NPCUtils.towers[enemyTeamInfo.enemyTeamNumber][LOrR]
        let font = GameRules.NPCUtils.towers[enemyTeamInfo.enemyTeamNumber][2]
        if (tower == undefined || !IsValidEntity(tower) || !tower.IsAlive()) {
            tower = font
        } else { //一塔存活
            tower = tower.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D() < font.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D() ? tower: font
        }
        let l = tower.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D()
        if (l > unit.Script_GetAttackRange()) {
            for (const b of GameRules.NPCUtils.buildings[enemyTeamInfo.enemyTeamNumber]) {
                if (b != null && b != undefined) {
                    let building = EntIndexToHScript(b) as CDOTA_BaseNPC;
                    if (building != null && building != undefined && IsValidEntity(building) && building.IsAlive != undefined && building.IsAlive()) {
                        let nl = building.GetAbsOrigin().__sub(unit.GetAbsOrigin()).Length2D()
                        if (nl < unit.GetAcquisitionRange()) {
                            if (nl < l) {
                                tower = building
                                l = nl
                            }
                        }
                        vaildBuilding.push(b)
                    }
                }
            }
        }
        GameRules.NPCUtils.buildings[enemyTeamInfo.enemyTeamNumber] = vaildBuilding
        GameRules.Debug.DebugPrint("目标："+tower.GetUnitName()+"——"+tower.towerIndex)
        this.tarU = tower
    }
}


